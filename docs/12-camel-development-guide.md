# SGASpace CAMEL 开发指南

## 🐪 CAMEL 框架集成概述

SGASpace 使用 CAMEL (Communicative Agents for "Mind" Exploration of Large Scale Language Model Society) 作为核心智能体引擎。CAMEL 提供了强大的多智能体协作能力，支持角色扮演、任务分解和工具集成。

### 集成架构

```
SGASpace (TypeScript/Next.js)
    ↕️ 进程间通信 (JSON over stdin/stdout)
CAMEL Framework (Python)
    ↕️ 智能体协作
LLM Models (OpenAI/Anthropic/Local)
```

## 🔧 开发环境设置

### 1. Python 环境配置

```bash
# 创建Python虚拟环境
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装CAMEL框架
pip install camel-ai[all]

# 安装其他依赖
pip install -r requirements.txt
```

### 2. 环境变量配置

```bash
# .env.local
# CAMEL 相关配置
OPENAI_API_KEY="your-openai-api-key"
ANTHROPIC_API_KEY="your-anthropic-api-key"  # 可选
CAMEL_LOG_LEVEL="INFO"
CAMEL_CACHE_DIR="./cache/camel"

# Python 服务配置
PYTHON_SERVICE_PORT="8001"
CAMEL_SERVICE_URL="http://localhost:8001"
```

## 🤖 CAMEL 智能体开发

### 1. 创建自定义智能体

```python
# server/camel/agents/custom_agent.py
from camel.agents import ChatAgent
from camel.models import ModelFactory
from camel.types import ModelPlatformType, ModelType
from camel.toolkits import SearchToolkit, ThinkingToolkit

class SGASpaceCustomAgent(ChatAgent):
    """SGASpace自定义智能体基类"""
    
    def __init__(self, role_name: str, system_message: str, **kwargs):
        # 创建模型
        model = ModelFactory.create(
            model_platform=ModelPlatformType.OPENAI,
            model_type=ModelType.GPT_4O_MINI
        )
        
        super().__init__(
            system_message=system_message,
            model=model,
            **kwargs
        )
        
        self.role_name = role_name
        self.setup_toolkits()
    
    def setup_toolkits(self):
        """设置智能体工具包"""
        # 基础工具包
        self.add_toolkit(ThinkingToolkit())
        
        # 根据角色添加特定工具
        if 'knowledge' in self.role_name.lower():
            self.add_toolkit(SearchToolkit())
    
    async def execute_task(self, task: str, context: dict = None) -> dict:
        """执行任务的标准接口"""
        try:
            # 准备输入消息
            input_message = self.prepare_input_message(task, context)
            
            # 执行智能体
            response = self.step(input_message)
            
            return {
                'success': True,
                'result': {
                    'content': response.msg.content,
                    'role': response.msg.role_name,
                    'metadata': response.info
                },
                'usage': response.usage if hasattr(response, 'usage') else None
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def prepare_input_message(self, task: str, context: dict = None) -> str:
        """准备输入消息"""
        message = f"任务: {task}"
        
        if context:
            message += f"\n\n上下文信息:\n{json.dumps(context, ensure_ascii=False, indent=2)}"
        
        return message

class TaskPlanningAgent(SGASpaceCustomAgent):
    """任务规划智能体"""
    
    def __init__(self):
        system_message = """
你是一个专业的任务规划专家。你的主要职责是：

1. 分析复杂任务并分解为可执行的子任务
2. 制定详细的执行计划和时间安排
3. 识别任务依赖关系和资源需求
4. 分配任务给合适的智能体或团队成员
5. 监控任务执行进度并调整计划

工作原则：
- 确保任务分解的逻辑性和完整性
- 考虑资源约束和时间限制
- 提供清晰的执行步骤和验收标准
- 识别潜在风险并制定应对措施

请始终以结构化的方式输出任务计划。
        """
        
        super().__init__("TaskPlanner", system_message)
        
        # 添加任务规划专用工具
        from camel.toolkits import TaskPlanningToolkit
        self.add_toolkit(TaskPlanningToolkit())

class KnowledgeManagementAgent(SGASpaceCustomAgent):
    """知识管理智能体"""
    
    def __init__(self):
        system_message = """
你是一个专业的知识管理专家。你的主要职责是：

1. 检索和整理相关知识信息
2. 回答基于知识库的问题
3. 总结和分析文档内容
4. 提供准确的信息支持和引用

工作原则：
- 确保信息的准确性和可靠性
- 提供信息来源和引用
- 以清晰易懂的方式组织信息
- 识别知识缺口并建议补充

请始终基于可靠的信息源提供答案。
        """
        
        super().__init__("KnowledgeManager", system_message)
        
        # 添加知识管理专用工具
        from camel.toolkits import SearchToolkit
        self.add_toolkit(SearchToolkit())
```

### 2. CAMEL Workforce 集成

```python
# server/camel/workforce/sga_workforce.py
from camel.societies.workforce import Workforce
from camel.agents import ChatAgent
from camel.models import ModelFactory
from camel.types import ModelPlatformType, ModelType
import asyncio
import json

class SGASpaceWorkforce(Workforce):
    """SGASpace定制的Workforce实现"""
    
    def __init__(self, workforce_id: str, config: dict):
        super().__init__(workforce_id)
        self.config = config
        self.setup_workers()
    
    def setup_workers(self):
        """根据配置设置工作者"""
        for worker_config in self.config.get('workers', []):
            agent = self.create_worker_agent(worker_config)
            self.add_single_agent_worker(
                worker_config['role'],
                worker=agent
            )
    
    def create_worker_agent(self, worker_config: dict) -> ChatAgent:
        """创建工作者智能体"""
        model = ModelFactory.create(
            model_platform=ModelPlatformType.OPENAI,
            model_type=ModelType.GPT_4O_MINI
        )
        
        agent = ChatAgent(
            system_message=worker_config['system_message'],
            model=model
        )
        
        # 添加工具包
        for toolkit_name in worker_config.get('toolkits', []):
            toolkit = self.create_toolkit(toolkit_name)
            if toolkit:
                agent.add_toolkit(toolkit)
        
        return agent
    
    def create_toolkit(self, toolkit_name: str):
        """创建工具包实例"""
        from camel.toolkits import (
            SearchToolkit, 
            CodeExecutionToolkit, 
            TaskPlanningToolkit,
            ThinkingToolkit
        )
        
        toolkit_map = {
            'SearchToolkit': SearchToolkit,
            'CodeExecutionToolkit': CodeExecutionToolkit,
            'TaskPlanningToolkit': TaskPlanningToolkit,
            'ThinkingToolkit': ThinkingToolkit
        }
        
        toolkit_class = toolkit_map.get(toolkit_name)
        return toolkit_class() if toolkit_class else None
    
    async def execute_task(self, task: str) -> dict:
        """执行任务并返回结果"""
        try:
            # 使用Workforce处理任务
            result = await self.process_task(task)
            
            return {
                'success': True,
                'result': result,
                'task_history': self.get_task_history(),
                'worker_stats': self.get_worker_statistics()
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_worker_statistics(self) -> dict:
        """获取工作者统计信息"""
        stats = {}
        for role, worker in self.workers.items():
            stats[role] = {
                'total_tasks': getattr(worker, 'total_tasks', 0),
                'completed_tasks': getattr(worker, 'completed_tasks', 0),
                'average_response_time': getattr(worker, 'avg_response_time', 0)
            }
        return stats
```

### 3. 角色扮演模式集成

```python
# server/camel/role_playing/sga_role_playing.py
from camel.societies import RolePlaying
from camel.agents import ChatAgent
from camel.models import ModelFactory
from camel.types import ModelPlatformType, ModelType, TaskType

class SGASpaceRolePlaying(RolePlaying):
    """SGASpace定制的角色扮演实现"""
    
    def __init__(self, config: dict):
        self.config = config
        
        super().__init__(
            assistant_role_name=config['assistant_role'],
            user_role_name=config['user_role'],
            task_prompt=config['task_prompt'],
            with_task_specify=config.get('with_task_specify', True),
            with_task_planner=config.get('with_task_planner', False),
            with_critic_in_the_loop=config.get('with_critic', False),
            task_type=TaskType.AI_SOCIETY
        )
    
    async def run_conversation(self, max_turns: int = 10) -> dict:
        """运行对话并返回结果"""
        self.init_chat()
        
        conversation_history = []
        
        for turn in range(max_turns):
            try:
                # 执行一轮对话
                assistant_response, user_response = self.step()
                
                # 记录对话历史
                conversation_history.append({
                    'turn': turn + 1,
                    'assistant': {
                        'role': assistant_response.msg.role_name,
                        'content': assistant_response.msg.content
                    },
                    'user': {
                        'role': user_response.msg.role_name,
                        'content': user_response.msg.content
                    }
                })
                
                # 检查是否结束
                if assistant_response.terminated or user_response.terminated:
                    break
                    
            except Exception as e:
                conversation_history.append({
                    'turn': turn + 1,
                    'error': str(e)
                })
                break
        
        return {
            'success': True,
            'conversation_history': conversation_history,
            'total_turns': len(conversation_history),
            'chat_history': self.get_chat_history()
        }
```

## 🔗 Node.js 与 Python 通信

### 1. Python 服务启动器

```typescript
// server/camel/python-service-manager.ts
import { spawn, ChildProcess } from 'child_process'
import { EventEmitter } from 'events'
import path from 'path'

export class PythonServiceManager extends EventEmitter {
  private pythonProcess: ChildProcess | null = null
  private isReady = false
  private messageQueue: any[] = []
  
  async start(): Promise<void> {
    return new Promise((resolve, reject) => {
      const pythonScript = path.join(process.cwd(), 'server/camel/camel_service.py')
      
      this.pythonProcess = spawn('python', [pythonScript], {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: {
          ...process.env,
          PYTHONPATH: process.cwd()
        }
      })
      
      this.setupProcessHandlers()
      
      // 等待服务就绪
      this.once('service:ready', () => {
        this.isReady = true
        this.processMessageQueue()
        resolve()
      })
      
      this.once('service:error', reject)
      
      // 超时处理
      setTimeout(() => {
        if (!this.isReady) {
          reject(new Error('Python service startup timeout'))
        }
      }, 30000)
    })
  }
  
  async sendMessage(message: any): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!this.isReady || !this.pythonProcess) {
        this.messageQueue.push({ message, resolve, reject })
        return
      }
      
      const requestId = this.generateRequestId()
      const request = { ...message, requestId }
      
      // 监听响应
      this.once(`response:${requestId}`, resolve)
      this.once(`error:${requestId}`, reject)
      
      // 发送消息
      this.pythonProcess.stdin?.write(JSON.stringify(request) + '\n')
      
      // 设置超时
      setTimeout(() => {
        reject(new Error('Request timeout'))
      }, 60000) // 60秒超时
    })
  }
  
  private setupProcessHandlers(): void {
    if (!this.pythonProcess) return
    
    this.pythonProcess.stdout?.on('data', (data: Buffer) => {
      const lines = data.toString().split('\n').filter(line => line.trim())
      
      for (const line of lines) {
        try {
          const message = JSON.parse(line)
          this.handlePythonMessage(message)
        } catch (error) {
          console.error('Failed to parse Python message:', line)
        }
      }
    })
    
    this.pythonProcess.stderr?.on('data', (data: Buffer) => {
      console.error('Python service error:', data.toString())
    })
    
    this.pythonProcess.on('exit', (code) => {
      console.log(`Python service exited with code ${code}`)
      this.isReady = false
      this.emit('service:exited', code)
    })
  }
  
  private handlePythonMessage(message: any): void {
    switch (message.type) {
      case 'service_ready':
        this.emit('service:ready')
        break
      case 'response':
        this.emit(`response:${message.requestId}`, message.data)
        break
      case 'error':
        this.emit(`error:${message.requestId}`, new Error(message.error))
        break
      case 'log':
        console.log(`[CAMEL] ${message.level}: ${message.message}`)
        break
    }
  }
  
  async stop(): Promise<void> {
    if (this.pythonProcess) {
      this.pythonProcess.kill('SIGTERM')
      this.pythonProcess = null
      this.isReady = false
    }
  }
}
```

### 2. 智能体服务接口

```typescript
// server/services/camel-integration.service.ts
export class CAMELIntegrationService {
  private pythonService: PythonServiceManager
  private agentInstances: Map<string, any> = new Map()
  
  constructor() {
    this.pythonService = new PythonServiceManager()
  }
  
  async initialize(): Promise<void> {
    await this.pythonService.start()
    console.log('CAMEL integration service initialized')
  }
  
  async createAgent(agentConfig: AgentConfig): Promise<string> {
    const response = await this.pythonService.sendMessage({
      type: 'create_agent',
      config: {
        id: agentConfig.id,
        type: agentConfig.type,
        name: agentConfig.name,
        description: agentConfig.description,
        system_message: this.generateSystemMessage(agentConfig),
        toolkits: this.getAgentToolkits(agentConfig.type)
      }
    })
    
    if (!response.success) {
      throw new Error(`Failed to create CAMEL agent: ${response.error}`)
    }
    
    // 缓存智能体实例信息
    this.agentInstances.set(agentConfig.id, {
      config: agentConfig,
      camelId: response.agent_id,
      createdAt: new Date()
    })
    
    return response.agent_id
  }
  
  async executeAgent(agentId: string, input: any): Promise<any> {
    const agentInstance = this.agentInstances.get(agentId)
    if (!agentInstance) {
      throw new Error(`Agent instance not found: ${agentId}`)
    }
    
    const response = await this.pythonService.sendMessage({
      type: 'execute_agent',
      agent_id: agentInstance.camelId,
      input: {
        message: input.message || input.task,
        context: input.context
      }
    })
    
    if (!response.success) {
      throw new Error(`Agent execution failed: ${response.error}`)
    }
    
    return response.result
  }
  
  async createWorkforce(workforceConfig: WorkforceConfig): Promise<string> {
    const response = await this.pythonService.sendMessage({
      type: 'create_workforce',
      config: {
        id: workforceConfig.id,
        workers: workforceConfig.workers.map(worker => ({
          role: worker.role,
          system_message: worker.systemMessage,
          toolkits: worker.toolkits || []
        }))
      }
    })
    
    if (!response.success) {
      throw new Error(`Failed to create workforce: ${response.error}`)
    }
    
    return response.workforce_id
  }
  
  async executeWorkforceTask(workforceId: string, task: string): Promise<any> {
    const response = await this.pythonService.sendMessage({
      type: 'execute_workforce',
      workforce_id: workforceId,
      task
    })
    
    if (!response.success) {
      throw new Error(`Workforce execution failed: ${response.error}`)
    }
    
    return response.result
  }
  
  private generateSystemMessage(agentConfig: AgentConfig): string {
    const roleTemplates = {
      TASK_PLANNING: `
你是一个专业的任务规划专家。你的名字是${agentConfig.name}。

主要职责：
1. 分析复杂任务并分解为子任务
2. 制定详细的执行计划
3. 分配任务给合适的智能体
4. 监控任务执行进度

${agentConfig.description ? `特殊说明：${agentConfig.description}` : ''}

请始终以结构化的方式提供任务规划。
      `,
      KNOWLEDGE_MANAGEMENT: `
你是一个专业的知识管理专家。你的名字是${agentConfig.name}。

主要职责：
1. 检索相关知识和信息
2. 整理和总结文档内容
3. 回答基于知识库的问题
4. 管理和组织知识资产

${agentConfig.description ? `特殊说明：${agentConfig.description}` : ''}

请确保提供准确、有据可依的信息。
      `,
      DECISION_SUPPORT: `
你是一个专业的决策支持分析师。你的名字是${agentConfig.name}。

主要职责：
1. 分析数据和信息
2. 评估风险和机会
3. 提供决策建议
4. 生成分析报告

${agentConfig.description ? `特殊说明：${agentConfig.description}` : ''}

请确保你的分析客观、全面、有据可依。
      `
    }
    
    return roleTemplates[agentConfig.type] || `你是一个专业的${agentConfig.name}智能体。${agentConfig.description || ''}`
  }
  
  private getAgentToolkits(agentType: AgentType): string[] {
    const toolkitMap = {
      TASK_PLANNING: ['TaskPlanningToolkit', 'ThinkingToolkit'],
      KNOWLEDGE_MANAGEMENT: ['SearchToolkit'],
      DECISION_SUPPORT: ['ThinkingToolkit'],
      TOOL_INTEGRATION: ['CodeExecutionToolkit'],
      COLLABORATION: ['ThinkingToolkit']
    }
    
    return toolkitMap[agentType] || []
  }
}
```

## 🚀 使用示例

### 1. 创建和使用智能体

```typescript
// 在tRPC路由中使用CAMEL智能体
export const agentRouter = createTRPCRouter({
  execute: protectedProcedure
    .input(z.object({
      agentId: z.string(),
      message: z.string(),
      context: z.any().optional()
    }))
    .mutation(async ({ input, ctx }) => {
      const camelService = new CAMELIntegrationService()
      
      try {
        const result = await camelService.executeAgent(input.agentId, {
          message: input.message,
          context: input.context
        })
        
        // 记录执行历史
        await ctx.db.agentExecution.create({
          data: {
            agentId: input.agentId,
            input: input,
            output: result,
            status: 'COMPLETED'
          }
        })
        
        return result
      } catch (error) {
        // 记录错误
        await ctx.db.agentExecution.create({
          data: {
            agentId: input.agentId,
            input: input,
            error: error.message,
            status: 'FAILED'
          }
        })
        
        throw error
      }
    })
})
```

### 2. 工作流中使用CAMEL Workforce

```typescript
// server/workflow/camel-workflow-node.ts
export class CAMELWorkflowNode extends BaseWorkflowNode {
  async execute(context: ExecutionContext): Promise<any> {
    const { workforceConfig, task } = this.config
    
    // 创建CAMEL Workforce
    const camelService = new CAMELIntegrationService()
    const workforceId = await camelService.createWorkforce(workforceConfig)
    
    // 执行任务
    const result = await camelService.executeWorkforceTask(workforceId, task)
    
    return result
  }
}
```

---

**文档版本**: 1.0.0  
**最后更新**: 2024-12-19  
**CAMEL 集成指南**: v1.0
