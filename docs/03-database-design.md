# SGASpace 数据库设计

## 🗄️ 数据库架构概览

SGASpace 采用**多数据库混合架构**，针对不同类型的数据选择最适合的存储方案：

| 数据库类型 | 产品选型 | 主要用途 | 选择理由 |
|-----------|---------|---------|---------|
| **关系型数据库** | PostgreSQL 15 | 业务数据、用户管理、工作流定义 | ACID支持、JSON字段、成熟稳定 |
| **缓存数据库** | Redis 7 | 会话缓存、实时状态、消息队列 | 高性能、丰富数据结构 |
| **向量数据库** | Qdrant | 知识库向量、语义检索 | 高性能向量检索、易于部署 |
| **对象存储** | MinIO | 文件存储、文档管理 | S3兼容、本地部署 |

## 📊 核心数据模型

### 实体关系图

```mermaid
erDiagram
    TENANT ||--o{ USER : contains
    TENANT ||--o{ AGENT : contains
    TENANT ||--o{ WORKFLOW : contains
    TENANT ||--o{ KNOWLEDGE_BASE : contains
    
    USER ||--o{ AGENT : creates
    USER ||--o{ WORKFLOW : creates
    USER ||--o{ DOCUMENT : uploads
    
    AGENT ||--o{ WORKFLOW_AGENT : participates
    WORKFLOW ||--o{ WORKFLOW_AGENT : includes
    WORKFLOW ||--o{ WORKFLOW_EXECUTION : executes
    
    WORKFLOW_EXECUTION ||--o{ AGENT_EXECUTION : contains
    AGENT_EXECUTION ||--o{ EXECUTION_LOG : generates
    
    KNOWLEDGE_BASE ||--o{ DOCUMENT : contains
    DOCUMENT ||--o{ DOCUMENT_CHUNK : splits
    
    TENANT {
        string id PK
        string name
        string slug
        json settings
        string status
        datetime created_at
        datetime updated_at
    }
    
    USER {
        string id PK
        string tenant_id FK
        string email
        string name
        string password_hash
        string role
        json preferences
        datetime last_login_at
        datetime created_at
        datetime updated_at
    }
    
    AGENT {
        string id PK
        string tenant_id FK
        string created_by_id FK
        string name
        string description
        string type
        json config
        string status
        json capabilities
        datetime created_at
        datetime updated_at
    }
    
    WORKFLOW {
        string id PK
        string tenant_id FK
        string created_by_id FK
        string name
        string description
        json definition
        string status
        json metadata
        datetime created_at
        datetime updated_at
    }
```

## 🏗️ Prisma Schema 定义

### 基础模型

```prisma
// prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 租户模型
model Tenant {
  id        String      @id @default(cuid())
  name      String
  slug      String      @unique
  settings  Json        @default("{}")
  status    TenantStatus @default(ACTIVE)
  createdAt DateTime    @default(now()) @map("created_at")
  updatedAt DateTime    @updatedAt @map("updated_at")

  // 关联关系
  users         User[]
  agents        Agent[]
  workflows     Workflow[]
  knowledgeBases KnowledgeBase[]
  documents     Document[]

  @@map("tenants")
}

// 用户模型
model User {
  id           String    @id @default(cuid())
  tenantId     String    @map("tenant_id")
  email        String    @unique
  name         String?
  passwordHash String    @map("password_hash")
  role         UserRole  @default(USER)
  preferences  Json      @default("{}")
  lastLoginAt  DateTime? @map("last_login_at")
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @updatedAt @map("updated_at")

  // 关联关系
  tenant    Tenant     @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  agents    Agent[]
  workflows Workflow[]
  documents Document[]

  @@map("users")
  @@index([tenantId, email])
  @@index([tenantId, role])
}

// 智能体模型
model Agent {
  id           String       @id @default(cuid())
  tenantId     String       @map("tenant_id")
  createdById  String       @map("created_by_id")
  name         String
  description  String?
  type         AgentType
  config       Json         @default("{}")
  status       AgentStatus  @default(INACTIVE)
  capabilities Json         @default("[]")
  createdAt    DateTime     @default(now()) @map("created_at")
  updatedAt    DateTime     @updatedAt @map("updated_at")

  // 关联关系
  tenant          Tenant           @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy       User             @relation(fields: [createdById], references: [id])
  workflowAgents  WorkflowAgent[]
  executions      AgentExecution[]

  @@map("agents")
  @@index([tenantId, type])
  @@index([tenantId, status])
}

// 工作流模型
model Workflow {
  id          String         @id @default(cuid())
  tenantId    String         @map("tenant_id")
  createdById String         @map("created_by_id")
  name        String
  description String?
  definition  Json           @default("{}")
  status      WorkflowStatus @default(DRAFT)
  metadata    Json           @default("{}")
  createdAt   DateTime       @default(now()) @map("created_at")
  updatedAt   DateTime       @updatedAt @map("updated_at")

  // 关联关系
  tenant         Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy      User                @relation(fields: [createdById], references: [id])
  agents         WorkflowAgent[]
  executions     WorkflowExecution[]

  @@map("workflows")
  @@index([tenantId, status])
}
```

### 关联模型

```prisma
// 工作流-智能体关联
model WorkflowAgent {
  id         String @id @default(cuid())
  workflowId String @map("workflow_id")
  agentId    String @map("agent_id")
  nodeId     String @map("node_id")
  config     Json   @default("{}")
  position   Json   @default("{}")

  // 关联关系
  workflow Workflow @relation(fields: [workflowId], references: [id], onDelete: Cascade)
  agent    Agent    @relation(fields: [agentId], references: [id], onDelete: Cascade)

  @@unique([workflowId, nodeId])
  @@map("workflow_agents")
}

// 工作流执行记录
model WorkflowExecution {
  id         String            @id @default(cuid())
  workflowId String            @map("workflow_id")
  status     ExecutionStatus   @default(PENDING)
  input      Json?
  output     Json?
  error      String?
  metadata   Json              @default("{}")
  startedAt  DateTime          @default(now()) @map("started_at")
  finishedAt DateTime?         @map("finished_at")

  // 关联关系
  workflow        Workflow         @relation(fields: [workflowId], references: [id], onDelete: Cascade)
  agentExecutions AgentExecution[]

  @@map("workflow_executions")
  @@index([workflowId, status])
  @@index([startedAt])
}

// 智能体执行记录
model AgentExecution {
  id                  String            @id @default(cuid())
  workflowExecutionId String            @map("workflow_execution_id")
  agentId             String            @map("agent_id")
  nodeId              String            @map("node_id")
  status              ExecutionStatus   @default(PENDING)
  input               Json?
  output              Json?
  error               String?
  metadata            Json              @default("{}")
  startedAt           DateTime          @default(now()) @map("started_at")
  finishedAt          DateTime?         @map("finished_at")

  // 关联关系
  workflowExecution WorkflowExecution @relation(fields: [workflowExecutionId], references: [id], onDelete: Cascade)
  agent             Agent             @relation(fields: [agentId], references: [id])
  logs              ExecutionLog[]

  @@map("agent_executions")
  @@index([workflowExecutionId, status])
  @@index([agentId, status])
}

// 执行日志
model ExecutionLog {
  id               String         @id @default(cuid())
  agentExecutionId String         @map("agent_execution_id")
  level            LogLevel       @default(INFO)
  message          String
  data             Json?
  timestamp        DateTime       @default(now())

  // 关联关系
  agentExecution AgentExecution @relation(fields: [agentExecutionId], references: [id], onDelete: Cascade)

  @@map("execution_logs")
  @@index([agentExecutionId, timestamp])
  @@index([level, timestamp])
}
```

### 知识库模型

```prisma
// 知识库
model KnowledgeBase {
  id          String    @id @default(cuid())
  tenantId    String    @map("tenant_id")
  name        String
  description String?
  settings    Json      @default("{}")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  // 关联关系
  tenant    Tenant     @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  documents Document[]

  @@map("knowledge_bases")
  @@index([tenantId])
}

// 文档
model Document {
  id              String         @id @default(cuid())
  tenantId        String         @map("tenant_id")
  knowledgeBaseId String?        @map("knowledge_base_id")
  uploadedById    String         @map("uploaded_by_id")
  filename        String
  originalName    String         @map("original_name")
  mimeType        String         @map("mime_type")
  size            Int
  path            String
  status          DocumentStatus @default(PENDING)
  metadata        Json           @default("{}")
  createdAt       DateTime       @default(now()) @map("created_at")
  updatedAt       DateTime       @updatedAt @map("updated_at")

  // 关联关系
  tenant        Tenant         @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  knowledgeBase KnowledgeBase? @relation(fields: [knowledgeBaseId], references: [id])
  uploadedBy    User           @relation(fields: [uploadedById], references: [id])
  chunks        DocumentChunk[]

  @@map("documents")
  @@index([tenantId, status])
  @@index([knowledgeBaseId])
}

// 文档分块
model DocumentChunk {
  id         String @id @default(cuid())
  documentId String @map("document_id")
  chunkIndex Int    @map("chunk_index")
  content    String
  metadata   Json   @default("{}")
  vectorId   String @map("vector_id") // Qdrant中的向量ID

  // 关联关系
  document Document @relation(fields: [documentId], references: [id], onDelete: Cascade)

  @@unique([documentId, chunkIndex])
  @@map("document_chunks")
  @@index([documentId])
}
```

### 枚举类型

```prisma
enum TenantStatus {
  ACTIVE
  SUSPENDED
  DELETED
}

enum UserRole {
  ADMIN
  MANAGER
  USER
  VIEWER
}

enum AgentType {
  TASK_PLANNING
  KNOWLEDGE_MANAGEMENT
  DECISION_SUPPORT
  TOOL_INTEGRATION
  COLLABORATION
  CUSTOM
}

enum AgentStatus {
  ACTIVE
  INACTIVE
  ERROR
  MAINTENANCE
}

enum WorkflowStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
  DEPRECATED
}

enum ExecutionStatus {
  PENDING
  RUNNING
  COMPLETED
  FAILED
  CANCELLED
  TIMEOUT
}

enum LogLevel {
  DEBUG
  INFO
  WARN
  ERROR
  FATAL
}

enum DocumentStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
}
```

## 🔍 索引优化策略

### 性能关键索引

```sql
-- 租户相关查询优化
CREATE INDEX CONCURRENTLY idx_users_tenant_email ON users(tenant_id, email);
CREATE INDEX CONCURRENTLY idx_agents_tenant_type_status ON agents(tenant_id, type, status);
CREATE INDEX CONCURRENTLY idx_workflows_tenant_status ON workflows(tenant_id, status);

-- 执行记录查询优化
CREATE INDEX CONCURRENTLY idx_workflow_executions_workflow_started ON workflow_executions(workflow_id, started_at DESC);
CREATE INDEX CONCURRENTLY idx_agent_executions_agent_started ON agent_executions(agent_id, started_at DESC);

-- 日志查询优化
CREATE INDEX CONCURRENTLY idx_execution_logs_execution_timestamp ON execution_logs(agent_execution_id, timestamp DESC);
CREATE INDEX CONCURRENTLY idx_execution_logs_level_timestamp ON execution_logs(level, timestamp DESC);

-- 知识库查询优化
CREATE INDEX CONCURRENTLY idx_documents_tenant_status ON documents(tenant_id, status);
CREATE INDEX CONCURRENTLY idx_document_chunks_document ON document_chunks(document_id);

-- 全文搜索索引
CREATE INDEX CONCURRENTLY idx_documents_content_search ON documents USING gin(to_tsvector('english', filename || ' ' || COALESCE(metadata->>'title', '')));
```

### 分区策略

```sql
-- 执行日志按时间分区
CREATE TABLE execution_logs_y2024m12 PARTITION OF execution_logs
FOR VALUES FROM ('2024-12-01') TO ('2025-01-01');

-- 自动分区管理
CREATE OR REPLACE FUNCTION create_monthly_partition()
RETURNS void AS $$
DECLARE
    start_date date;
    end_date date;
    partition_name text;
BEGIN
    start_date := date_trunc('month', CURRENT_DATE + interval '1 month');
    end_date := start_date + interval '1 month';
    partition_name := 'execution_logs_y' || to_char(start_date, 'YYYY') || 'm' || to_char(start_date, 'MM');
    
    EXECUTE format('CREATE TABLE IF NOT EXISTS %I PARTITION OF execution_logs FOR VALUES FROM (%L) TO (%L)',
                   partition_name, start_date, end_date);
END;
$$ LANGUAGE plpgsql;
```

## 🔐 数据安全设计

### 行级安全策略

```sql
-- 启用行级安全
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE agents ENABLE ROW LEVEL SECURITY;
ALTER TABLE workflows ENABLE ROW LEVEL SECURITY;
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;

-- 租户隔离策略
CREATE POLICY tenant_isolation_users ON users
  FOR ALL TO authenticated_user
  USING (tenant_id = current_setting('app.current_tenant_id'));

CREATE POLICY tenant_isolation_agents ON agents
  FOR ALL TO authenticated_user
  USING (tenant_id = current_setting('app.current_tenant_id'));

CREATE POLICY tenant_isolation_workflows ON workflows
  FOR ALL TO authenticated_user
  USING (tenant_id = current_setting('app.current_tenant_id'));

CREATE POLICY tenant_isolation_documents ON documents
  FOR ALL TO authenticated_user
  USING (tenant_id = current_setting('app.current_tenant_id'));
```

### 数据加密

```typescript
// lib/encryption.ts
import crypto from 'crypto'
import bcrypt from 'bcryptjs'

export class EncryptionService {
  private readonly algorithm = 'aes-256-gcm'
  private readonly key = Buffer.from(process.env.ENCRYPTION_KEY!, 'hex')

  // 密码加密
  async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, 12)
  }

  async verifyPassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash)
  }

  // 敏感数据加密
  encrypt(text: string): string {
    const iv = crypto.randomBytes(16)
    const cipher = crypto.createCipher(this.algorithm, this.key)
    cipher.setAAD(Buffer.from('sgaspace', 'utf8'))

    let encrypted = cipher.update(text, 'utf8', 'hex')
    encrypted += cipher.final('hex')

    const authTag = cipher.getAuthTag()
    return `${iv.toString('hex')}:${authTag.toString('hex')}:${encrypted}`
  }

  decrypt(encryptedData: string): string {
    const [ivHex, authTagHex, encrypted] = encryptedData.split(':')
    const iv = Buffer.from(ivHex, 'hex')
    const authTag = Buffer.from(authTagHex, 'hex')

    const decipher = crypto.createDecipher(this.algorithm, this.key)
    decipher.setAAD(Buffer.from('sgaspace', 'utf8'))
    decipher.setAuthTag(authTag)

    let decrypted = decipher.update(encrypted, 'hex', 'utf8')
    decrypted += decipher.final('utf8')

    return decrypted
  }
}
```

---

**文档版本**: 1.0.0  
**最后更新**: 2024-12-19  
**数据库版本**: PostgreSQL 15+
