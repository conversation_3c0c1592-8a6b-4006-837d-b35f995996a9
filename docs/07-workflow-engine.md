# SGASpace 工作流引擎设计

## 🔄 工作流引擎概览

SGASpace 工作流引擎是一个**基于 Redis 队列的事件驱动**工作流执行系统，支持复杂的业务流程自动化。通过拖拽式设计器，用户可以轻松创建包含多个智能体协作的工作流。

### 核心特性

1. **可视化设计** - 基于React Flow的拖拽式工作流编辑器
2. **智能体集成** - 无缝集成CAMEL智能体服务
3. **流程控制** - 支持条件分支、循环、并行执行
4. **Redis队列调度** - 基于BullMQ的可靠任务调度
5. **实时状态同步** - Redis Pub/Sub实时状态更新
6. **错误处理** - 完善的异常处理和重试机制
7. **版本管理** - 工作流版本控制和回滚

## 🏗️ 工作流架构设计

### 整体架构图

```mermaid
graph TB
    subgraph "设计层"
        A[React Flow 工作流设计器]
        B[节点库]
        C[模板库]
    end

    subgraph "API层"
        D[tRPC 工作流API]
        E[WebSocket 状态推送]
    end

    subgraph "执行层"
        F[工作流执行引擎]
        G[Redis BullMQ 队列]
        H[状态管理器]
    end

    subgraph "智能体服务"
        I[CAMEL Service]
        J[Agent 执行器]
    end

    subgraph "存储层"
        K[PostgreSQL<br/>工作流定义/执行历史]
        L[Redis<br/>状态缓存/队列/实时]
    end

    A --> D
    D --> F
    F --> G
    G --> H
    F --> I
    I --> J
    F --> K
    H --> L
    E --> A
```

### 工作流 DSL 定义

```typescript
// types/workflow-dsl.ts
export interface WorkflowDefinition {
  id: string
  name: string
  description?: string
  version: string
  nodes: WorkflowNode[]
  edges: WorkflowEdge[]
  variables: WorkflowVariable[]
  settings: WorkflowSettings
  metadata: WorkflowMetadata
}

export interface WorkflowNode {
  id: string
  type: NodeType
  position: { x: number; y: number }
  data: NodeData
  config: NodeConfig
  retry?: RetryConfig
  timeout?: number // 超时时间（秒）
}

export interface WorkflowEdge {
  id: string
  source: string
  target: string
  sourceHandle?: string
  targetHandle?: string
  condition?: EdgeCondition
  label?: string
}

export enum NodeType {
  START = 'start',
  END = 'end',
  AGENT = 'agent',
  CONDITION = 'condition',
  LOOP = 'loop',
  PARALLEL = 'parallel',
  TOOL = 'tool',
  DATA = 'data',
  DELAY = 'delay',
  WEBHOOK = 'webhook'
}

export interface RetryConfig {
  maxAttempts: number
  backoffType: 'fixed' | 'exponential' | 'linear'
  initialDelay: number // 毫秒
  maxDelay?: number
}

export interface NodeData {
  // 智能体节点
  agentId?: string
  agentType?: string
  input?: Record<string, any>

  // 条件节点
  conditions?: ConditionRule[]

  // 循环节点
  loopType?: 'for' | 'while' | 'forEach'
  loopConfig?: LoopConfig

  // 工具节点
  toolName?: string
  toolConfig?: Record<string, any>

  // 数据节点
  dataOperation?: 'transform' | 'filter' | 'merge'
  dataConfig?: Record<string, any>

  // 延时节点
  delayMs?: number

  // Webhook节点
  webhookUrl?: string
  webhookMethod?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  webhookHeaders?: Record<string, string>
}

export interface ConditionRule {
  field: string
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'contains' | 'startsWith' | 'endsWith'
  value: any
  logicalOperator?: 'AND' | 'OR'
}

export interface LoopConfig {
  // for循环
  start?: number
  end?: number
  step?: number

  // while循环
  condition?: ConditionRule[]

  // forEach循环
  arrayPath?: string
  itemVariable?: string

  // 通用配置
  maxIterations?: number
}
## 🚀 Redis 队列执行引擎

### BullMQ 队列配置

```typescript
// lib/workflow-queue.ts
import { Queue, Worker, Job } from 'bullmq'
import { Redis } from 'ioredis'

const redis = new Redis(process.env.REDIS_URL!)

// 工作流执行队列
export const workflowQueue = new Queue('workflow-execution', {
  connection: redis,
  defaultJobOptions: {
    removeOnComplete: 100,
    removeOnFail: 50,
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 2000,
    },
  },
})

// 节点执行队列
export const nodeQueue = new Queue('node-execution', {
  connection: redis,
  defaultJobOptions: {
    removeOnComplete: 200,
    removeOnFail: 100,
    attempts: 5,
    backoff: {
      type: 'exponential',
      delay: 1000,
    },
  },
})

// 工作流执行任务数据
export interface WorkflowExecutionJob {
  workflowId: string
  executionId: string
  input: Record<string, any>
  tenantId: string
  userId: string
  context: Record<string, any>
}

// 节点执行任务数据
export interface NodeExecutionJob {
  executionId: string
  nodeId: string
  nodeType: string
  nodeData: NodeData
  nodeConfig: NodeConfig
  input: Record<string, any>
  context: Record<string, any>
  tenantId: string
}
```

### 工作流执行引擎

```typescript
// lib/workflow-executor.ts
import { WorkflowExecutionJob, NodeExecutionJob } from './workflow-queue'
import { camelClient } from './camel-client'
import { realtimeService } from './realtime'

export class WorkflowExecutor {
  private redis: Redis

  constructor() {
    this.redis = new Redis(process.env.REDIS_URL!)
  }

  async executeWorkflow(job: Job<WorkflowExecutionJob>): Promise<void> {
    const { workflowId, executionId, input, tenantId, userId, context } = job.data

    try {
      // 1. 加载工作流定义
      const workflow = await this.loadWorkflow(workflowId, tenantId)

      // 2. 创建执行上下文
      const executionContext = {
        executionId,
        workflowId,
        tenantId,
        userId,
        input,
        context,
        variables: new Map<string, any>(),
        nodeResults: new Map<string, any>()
      }

      // 3. 查找起始节点
      const startNode = workflow.nodes.find(node => node.type === NodeType.START)
      if (!startNode) {
        throw new Error('No start node found in workflow')
      }

      // 4. 开始执行
      await this.executeNode(startNode, executionContext, workflow)

      // 5. 更新执行状态
      await this.updateExecutionStatus(executionId, 'COMPLETED')

    } catch (error) {
      console.error('Workflow execution failed:', error)
      await this.updateExecutionStatus(executionId, 'FAILED', error.message)
      throw error
    }
  }

  private async executeNode(
    node: WorkflowNode,
    context: ExecutionContext,
    workflow: WorkflowDefinition
  ): Promise<any> {

    // 更新节点状态为执行中
    await this.updateNodeStatus(context.executionId, node.id, 'RUNNING')

    try {
      let result: any

      switch (node.type) {
        case NodeType.START:
          result = context.input
          break

        case NodeType.AGENT:
          result = await this.executeAgentNode(node, context)
          break

        case NodeType.CONDITION:
          result = await this.executeConditionNode(node, context)
          break

        case NodeType.PARALLEL:
          result = await this.executeParallelNode(node, context, workflow)
          break

        case NodeType.TOOL:
          result = await this.executeToolNode(node, context)
          break

        case NodeType.END:
          result = context.nodeResults.get(this.getPreviousNodeId(node, workflow))
          break

        default:
          throw new Error(`Unsupported node type: ${node.type}`)
      }

      // 保存节点结果
      context.nodeResults.set(node.id, result)

      // 更新节点状态为完成
      await this.updateNodeStatus(context.executionId, node.id, 'COMPLETED', result)

      // 执行下一个节点
      if (node.type !== NodeType.END) {
        const nextNodes = this.getNextNodes(node, workflow, result)

        for (const nextNode of nextNodes) {
          await this.executeNode(nextNode, context, workflow)
        }
      }

      return result

    } catch (error) {
      await this.updateNodeStatus(context.executionId, node.id, 'FAILED', null, error.message)
      throw error
    }
  }
```

### 节点执行器实现

```typescript
// lib/workflow-executor.ts (续)

  private async executeAgentNode(node: WorkflowNode, context: ExecutionContext): Promise<any> {
    const { agentId, agentType, input } = node.data

    if (!agentId || !agentType) {
      throw new Error('Agent node missing agentId or agentType')
    }

    // 准备输入数据
    const agentInput = this.resolveNodeInput(input || {}, context)

    // 调用CAMEL服务执行智能体
    const result = await camelClient.executeAgentWithRetry({
      agentId,
      agentType,
      input: { message: agentInput.message || agentInput.task || JSON.stringify(agentInput) },
      context: agentInput.context || {},
      tenantId: context.tenantId,
      userId: context.userId
    })

    if (!result.success) {
      throw new Error(result.error || 'Agent execution failed')
    }

    return result.output
  }

  private async executeConditionNode(node: WorkflowNode, context: ExecutionContext): Promise<any> {
    const { conditions } = node.data

    if (!conditions || conditions.length === 0) {
      return { result: true, branch: 'default' }
    }

    // 评估条件
    const result = this.evaluateConditions(conditions, context)

    return { result, branch: result ? 'true' : 'false' }
  }

  private async executeParallelNode(
    node: WorkflowNode,
    context: ExecutionContext,
    workflow: WorkflowDefinition
  ): Promise<any> {
    const parallelNodes = this.getParallelBranches(node, workflow)

    // 并行执行所有分支
    const promises = parallelNodes.map(async (branchNode) => {
      const branchContext = { ...context }
      return this.executeNode(branchNode, branchContext, workflow)
    })

    const results = await Promise.allSettled(promises)

    // 处理结果
    const successResults = []
    const errors = []

    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        successResults.push(result.value)
      } else {
        errors.push({
          nodeId: parallelNodes[index].id,
          error: result.reason
        })
      }
    })

    if (errors.length > 0) {
      throw new Error(`Parallel execution failed: ${JSON.stringify(errors)}`)
    }

    return { results: successResults }
  }

  private async executeToolNode(node: WorkflowNode, context: ExecutionContext): Promise<any> {
    const { toolName, toolConfig } = node.data

    if (!toolName) {
      throw new Error('Tool node missing toolName')
    }

    // 解析工具配置
    const config = this.resolveNodeInput(toolConfig || {}, context)

    // 执行工具
    const toolExecutor = this.getToolExecutor(toolName)
    const result = await toolExecutor.execute(config)

    return result
  }

  private resolveNodeInput(input: Record<string, any>, context: ExecutionContext): Record<string, any> {
    const resolved: Record<string, any> = {}

    for (const [key, value] of Object.entries(input)) {
      if (typeof value === 'string' && value.startsWith('{{') && value.endsWith('}}')) {
        // 变量引用，如 {{variables.userName}} 或 {{nodes.node1.output}}
        const variablePath = value.slice(2, -2).trim()
        resolved[key] = this.resolveVariable(variablePath, context)
      } else {
        resolved[key] = value
      }
    }

    return resolved
  }

  private resolveVariable(path: string, context: ExecutionContext): any {
    const parts = path.split('.')

    if (parts[0] === 'variables') {
      return context.variables.get(parts.slice(1).join('.'))
    } else if (parts[0] === 'nodes') {
      const nodeId = parts[1]
      const resultPath = parts.slice(2).join('.')
      const nodeResult = context.nodeResults.get(nodeId)

      if (!nodeResult) {
        throw new Error(`Node result not found: ${nodeId}`)
      }

      return resultPath ? this.getNestedValue(nodeResult, resultPath) : nodeResult
    } else if (parts[0] === 'input') {
      const inputPath = parts.slice(1).join('.')
      return inputPath ? this.getNestedValue(context.input, inputPath) : context.input
    }

    throw new Error(`Unknown variable path: ${path}`)
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj)
  }
}
### Worker 进程配置

```typescript
// workers/workflow-worker.ts
import { Worker, Job } from 'bullmq'
import { WorkflowExecutor } from '../lib/workflow-executor'

const workflowExecutor = new WorkflowExecutor()

// 工作流执行 Worker
export const workflowWorker = new Worker(
  'workflow-execution',
  async (job: Job<WorkflowExecutionJob>) => {
    console.log(`Processing workflow execution: ${job.data.executionId}`)

    try {
      await workflowExecutor.executeWorkflow(job)
      console.log(`Workflow execution completed: ${job.data.executionId}`)
    } catch (error) {
      console.error(`Workflow execution failed: ${job.data.executionId}`, error)
      throw error
    }
  },
  {
    connection: new Redis(process.env.REDIS_URL!),
    concurrency: 5, // 并发执行数量
    removeOnComplete: 100,
    removeOnFail: 50,
  }
)

// 节点执行 Worker
export const nodeWorker = new Worker(
  'node-execution',
  async (job: Job<NodeExecutionJob>) => {
    console.log(`Processing node execution: ${job.data.nodeId}`)

    try {
      const result = await workflowExecutor.executeNodeJob(job)
      console.log(`Node execution completed: ${job.data.nodeId}`)
      return result
    } catch (error) {
      console.error(`Node execution failed: ${job.data.nodeId}`, error)
      throw error
    }
  },
  {
    connection: new Redis(process.env.REDIS_URL!),
    concurrency: 10, // 节点执行并发数更高
    removeOnComplete: 200,
    removeOnFail: 100,
  }
)

// 错误处理
workflowWorker.on('failed', (job, err) => {
  console.error(`Workflow job ${job?.id} failed:`, err)
})

nodeWorker.on('failed', (job, err) => {
  console.error(`Node job ${job?.id} failed:`, err)
})

// 优雅关闭
process.on('SIGINT', async () => {
  console.log('Shutting down workers...')
  await workflowWorker.close()
  await nodeWorker.close()
  process.exit(0)
})
```

## 📊 实时状态管理

### Redis 状态服务

```typescript
// lib/workflow-state.ts
export class WorkflowStateManager {
  private redis: Redis
  private publisher: Redis

  constructor() {
    this.redis = new Redis(process.env.REDIS_URL!)
    this.publisher = new Redis(process.env.REDIS_URL!)
  }

  async updateExecutionStatus(
    executionId: string,
    status: ExecutionStatus,
    error?: string
  ): Promise<void> {
    const key = `execution:${executionId}`
    const data = {
      status,
      updatedAt: new Date().toISOString(),
      ...(error && { error })
    }

    // 更新Redis状态
    await this.redis.hset(key, data)

    // 发布状态更新事件
    await this.publisher.publish(`execution:${executionId}:status`, JSON.stringify({
      type: 'execution-status',
      executionId,
      status,
      error,
      timestamp: Date.now()
    }))
  }

  async updateNodeStatus(
    executionId: string,
    nodeId: string,
    status: NodeStatus,
    result?: any,
    error?: string
  ): Promise<void> {
    const key = `execution:${executionId}:node:${nodeId}`
    const data = {
      status,
      updatedAt: new Date().toISOString(),
      ...(result && { result: JSON.stringify(result) }),
      ...(error && { error })
    }

    // 更新Redis状态
    await this.redis.hset(key, data)

    // 发布节点状态更新事件
    await this.publisher.publish(`execution:${executionId}:node`, JSON.stringify({
      type: 'node-status',
      executionId,
      nodeId,
      status,
      result,
      error,
      timestamp: Date.now()
    }))
  }

  async getExecutionStatus(executionId: string): Promise<ExecutionState | null> {
    const key = `execution:${executionId}`
    const data = await this.redis.hgetall(key)

    if (!data || Object.keys(data).length === 0) {
      return null
    }

    return {
      executionId,
      status: data.status as ExecutionStatus,
      updatedAt: data.updatedAt,
      error: data.error
    }
  }

  async getNodeStatus(executionId: string, nodeId: string): Promise<NodeState | null> {
    const key = `execution:${executionId}:node:${nodeId}`
    const data = await this.redis.hgetall(key)

    if (!data || Object.keys(data).length === 0) {
      return null
    }

    return {
      executionId,
      nodeId,
      status: data.status as NodeStatus,
      result: data.result ? JSON.parse(data.result) : null,
      error: data.error,
      updatedAt: data.updatedAt
    }
  }
}

// 类型定义
export type ExecutionStatus = 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED'
export type NodeStatus = 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'SKIPPED'

export interface ExecutionState {
  executionId: string
  status: ExecutionStatus
  updatedAt: string
  error?: string
}

export interface NodeState {
  executionId: string
  nodeId: string
  status: NodeStatus
  result?: any
  error?: string
  updatedAt: string
}
## 🎨 可视化工作流设计器

### React Flow 集成

```typescript
// components/workflow/workflow-designer.tsx
import ReactFlow, {
  Node,
  Edge,
  addEdge,
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  useReactFlow
} from 'reactflow'

export function WorkflowDesigner({ workflowId }: { workflowId: string }) {
  const [nodes, setNodes, onNodesChange] = useNodesState([])
  const [edges, setEdges, onEdgesChange] = useEdgesState([])
  const { project } = useReactFlow()

  // 自定义节点类型
  const nodeTypes = {
    agent: AgentNode,
    condition: ConditionNode,
    loop: LoopNode,
    parallel: ParallelNode,
    tool: ToolNode,
    data: DataNode
  }

  const onConnect = useCallback(
    (params) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  )

  const onDrop = useCallback(
    (event) => {
      event.preventDefault()

      const reactFlowBounds = event.currentTarget.getBoundingClientRect()
      const type = event.dataTransfer.getData('application/reactflow')
      const position = project({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      })

      const newNode = {
        id: generateId(),
        type,
        position,
        data: { label: `${type} node` },
      }

      setNodes((nds) => nds.concat(newNode))
    },
    [project, setNodes]
  )

  return (
    <div className="h-full w-full">
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onDrop={onDrop}
        onDragOver={(event) => event.preventDefault()}
        nodeTypes={nodeTypes}
        fitView
      >
        <Background />
        <Controls />
        <MiniMap />
      </ReactFlow>
    </div>
  )
}
```

### 智能体节点组件

```typescript
// components/workflow/nodes/agent-node.tsx
export function AgentNode({ data, selected }: NodeProps) {
  const [agent, setAgent] = useState<Agent | null>(null)
  const [isConfigOpen, setIsConfigOpen] = useState(false)

  return (
    <div className={cn(
      "bg-white border-2 rounded-lg shadow-sm min-w-[200px]",
      selected ? "border-blue-500" : "border-gray-200"
    )}>
      <Handle type="target" position={Position.Top} />

      <div className="p-4">
        <div className="flex items-center gap-2 mb-2">
          <Bot className="w-4 h-4 text-blue-500" />
          <span className="font-medium">智能体节点</span>
        </div>

        {agent ? (
          <div className="space-y-2">
            <div className="text-sm font-medium">{agent.name}</div>
            <div className="text-xs text-gray-500">{agent.type}</div>
            <div className="flex items-center gap-1">
              <div className={cn(
                "w-2 h-2 rounded-full",
                agent.status === 'ACTIVE' ? "bg-green-500" : "bg-gray-400"
              )} />
              <span className="text-xs">{agent.status}</span>
            </div>
          </div>
        ) : (
          <div className="text-sm text-gray-500">
            点击配置智能体
          </div>
        )}

        <Button
          variant="outline"
          size="sm"
          className="w-full mt-2"
          onClick={() => setIsConfigOpen(true)}
        >
          配置
        </Button>
      </div>

      <Handle type="source" position={Position.Bottom} />

      <AgentConfigDialog
        open={isConfigOpen}
        onOpenChange={setIsConfigOpen}
        onAgentSelect={setAgent}
      />
    </div>
  )
}
```

## 🔧 tRPC 工作流 API

### 工作流路由

```typescript
// server/api/routers/workflow.ts
export const workflowRouter = createTRPCRouter({
  create: protectedProcedure
    .input(z.object({
      name: z.string().min(1),
      description: z.string().optional(),
      definition: z.any() // WorkflowDefinition
    }))
    .mutation(async ({ input, ctx }) => {
      const workflow = await ctx.db.workflow.create({
        data: {
          ...input,
          tenantId: ctx.session.user.tenantId,
          createdById: ctx.session.user.id,
          status: 'DRAFT'
        }
      })

      return workflow
    }),

  execute: protectedProcedure
    .input(z.object({
      workflowId: z.string(),
      input: z.record(z.any()).optional()
    }))
    .mutation(async ({ input, ctx }) => {
      // 验证工作流权限
      const workflow = await ctx.db.workflow.findUnique({
        where: {
          id: input.workflowId,
          tenantId: ctx.session.user.tenantId
        }
      })

      if (!workflow) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Workflow not found'
        })
      }

      // 创建执行记录
      const execution = await ctx.db.workflowExecution.create({
        data: {
          workflowId: input.workflowId,
          input: input.input || {},
          status: 'PENDING',
          tenantId: ctx.session.user.tenantId,
          userId: ctx.session.user.id
        }
      })

      // 提交到队列执行
      await workflowQueue.add('execute-workflow', {
        workflowId: input.workflowId,
        executionId: execution.id,
        input: input.input || {},
        tenantId: ctx.session.user.tenantId,
        userId: ctx.session.user.id,
        context: {}
      })

      return { executionId: execution.id }
    }),

  getExecution: protectedProcedure
    .input(z.object({
      executionId: z.string()
    }))
    .query(async ({ input, ctx }) => {
      const execution = await ctx.db.workflowExecution.findUnique({
        where: {
          id: input.executionId,
          tenantId: ctx.session.user.tenantId
        },
        include: {
          workflow: true,
          agentExecutions: {
            include: {
              agent: true,
              logs: true
            }
          }
        }
      })

      if (!execution) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Execution not found'
        })
      }

      // 获取实时状态
      const stateManager = new WorkflowStateManager()
      const realtimeStatus = await stateManager.getExecutionStatus(input.executionId)

      return {
        ...execution,
        realtimeStatus
      }
    }),

  list: protectedProcedure
    .input(z.object({
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(100).default(20),
      status: z.enum(['DRAFT', 'PUBLISHED', 'ARCHIVED']).optional()
    }))
    .query(async ({ input, ctx }) => {
      const { page, limit, status } = input
      const skip = (page - 1) * limit

      const where = {
        tenantId: ctx.session.user.tenantId,
        ...(status && { status })
      }

      const [workflows, total] = await Promise.all([
        ctx.db.workflow.findMany({
          where,
          skip,
          take: limit,
          include: {
            _count: {
              select: { executions: true }
            },
            createdBy: {
              select: { name: true, email: true }
            }
          },
          orderBy: { updatedAt: 'desc' }
        }),
        ctx.db.workflow.count({ where })
      ])

      return {
        workflows,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })
})
```

## 📊 工作流监控与分析

### 实时状态监控

```typescript
// components/workflow/execution-monitor.tsx
export function ExecutionMonitor({ executionId }: { executionId: string }) {
  const [executionState, setExecutionState] = useState<ExecutionState | null>(null)
  const [nodeStates, setNodeStates] = useState<Map<string, NodeState>>(new Map())

  // 订阅实时状态更新
  useEffect(() => {
    const realtimeClient = new RealtimeClient(tenantId)

    realtimeClient.onWorkflowStatus = (data) => {
      if (data.executionId === executionId) {
        setExecutionState(data)
      }
    }

    realtimeClient.onAgentStatus = (data) => {
      if (data.executionId === executionId) {
        setNodeStates(prev => new Map(prev.set(data.nodeId, data)))
      }
    }

    return () => {
      realtimeClient.disconnect()
    }
  }, [executionId, tenantId])

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">执行监控</h3>
        <Badge variant={getStatusVariant(executionState?.status)}>
          {executionState?.status || 'UNKNOWN'}
        </Badge>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {Array.from(nodeStates.entries()).map(([nodeId, state]) => (
          <Card key={nodeId}>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">{nodeId}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <div className={cn(
                  "w-2 h-2 rounded-full",
                  getStatusColor(state.status)
                )} />
                <span className="text-sm">{state.status}</span>
              </div>
              {state.error && (
                <div className="mt-2 text-xs text-red-600">
                  {state.error}
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
```

---

**文档版本**: 2.0.0
**最后更新**: 2024-12-19
**工作流引擎版本**: v2.0 (Redis队列)
