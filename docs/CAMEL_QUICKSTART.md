# SGASpace + CAMEL 快速开始指南

## 🚀 5分钟快速体验

### 1. 环境准备

```bash
# 检查环境要求
node --version    # 需要 18+
python3 --version # 需要 3.9+
docker --version  # 需要 20+

# 克隆项目
git clone <your-repo>
cd sgaspace
```

### 2. 一键初始化

```bash
# 运行初始化脚本（包含CAMEL框架安装）
bash docs/rewrite/scripts/init-project.sh
```

这个脚本会自动：
- ✅ 创建完整项目结构
- ✅ 安装Node.js依赖
- ✅ 创建Python虚拟环境
- ✅ 安装CAMEL框架和相关依赖
- ✅ 启动数据库服务（PostgreSQL, Redis, Qdrant, MinIO）
- ✅ 初始化数据库结构

### 3. 启动服务

```bash
# 进入项目目录
cd sgaspace

# 配置环境变量
cp .env.example .env.local
# 编辑 .env.local，添加你的 OpenAI API Key

# 启动开发服务器
pnpm dev
```

### 4. 体验核心功能

访问 http://localhost:3000，您将看到：

1. **智能体管理** - 创建基于CAMEL的专业智能体
2. **工作流设计** - 拖拽式设计器，集成CAMEL智能体节点
3. **实时协作** - 多智能体协作的实时监控
4. **知识库** - 向量化知识检索

## 🐪 CAMEL 框架核心概念

### 1. 智能体类型

| CAMEL 智能体 | SGASpace 映射 | 主要能力 |
|-------------|--------------|---------|
| **TaskPlannerAgent** | 任务规划智能体 | 任务分解、计划制定、进度监控 |
| **ChatAgent + SearchToolkit** | 知识管理智能体 | 知识检索、内容生成、信息整理 |
| **ChatAgent + ThinkingToolkit** | 决策支持智能体 | 数据分析、风险评估、决策建议 |
| **ToolkitAgent** | 工具集成智能体 | API调用、系统集成、自动化操作 |
| **CriticAgent** | 质量控制智能体 | 结果评估、质量检查、改进建议 |

### 2. 协作模式

#### RolePlaying 模式
```python
# 两个智能体角色扮演对话
role_playing = RolePlaying(
    assistant_role_name="产品经理",
    user_role_name="技术负责人", 
    task_prompt="讨论新功能的技术实现方案"
)
```

#### Workforce 模式
```python
# 多个智能体协同工作
workforce = Workforce("product_development_team")
workforce.add_single_agent_worker("产品经理", product_manager_agent)
workforce.add_single_agent_worker("技术负责人", tech_lead_agent)
workforce.add_single_agent_worker("UI设计师", ui_designer_agent)
```

### 3. 工具生态

CAMEL 提供丰富的工具包：

- **SearchToolkit** - 网络搜索、信息检索
- **CodeExecutionToolkit** - 代码执行、结果验证
- **TaskPlanningToolkit** - 任务分解、计划制定
- **ThinkingToolkit** - 逻辑推理、问题分析
- **MathToolkit** - 数学计算、公式求解

## 🔧 开发示例

### 1. 创建智能体

```typescript
// 在SGASpace中创建CAMEL智能体
const agent = await api.agent.create.mutate({
  name: "产品分析师",
  type: "DECISION_SUPPORT",
  description: "专业的产品数据分析和决策支持",
  config: {
    camelConfig: {
      systemMessage: "你是一个专业的产品分析师...",
      toolkits: ["ThinkingToolkit", "SearchToolkit"],
      modelType: "GPT_4O_MINI"
    }
  }
})
```

### 2. 执行智能体任务

```typescript
// 执行智能体任务
const result = await api.agent.execute.mutate({
  agentId: agent.id,
  message: "分析我们产品的用户增长趋势",
  context: {
    userGrowthData: [...],
    timeRange: "last_3_months"
  }
})

console.log(result.content) // CAMEL智能体的分析结果
```

### 3. 创建工作流

```typescript
// 创建包含CAMEL智能体的工作流
const workflow = await api.workflow.create.mutate({
  name: "产品决策工作流",
  definition: {
    nodes: [
      {
        id: "data_analysis",
        type: "agent",
        data: {
          agentType: "DECISION_SUPPORT",
          task: "分析产品数据"
        }
      },
      {
        id: "risk_assessment", 
        type: "agent",
        data: {
          agentType: "DECISION_SUPPORT",
          task: "评估决策风险"
        }
      },
      {
        id: "final_recommendation",
        type: "workforce",
        data: {
          workers: ["产品经理", "技术负责人", "数据分析师"],
          task: "制定最终决策建议"
        }
      }
    ],
    edges: [
      { source: "data_analysis", target: "risk_assessment" },
      { source: "risk_assessment", target: "final_recommendation" }
    ]
  }
})
```

## 📊 CAMEL 集成优势

### 相比自建智能体框架的优势

| 维度 | 自建框架 | CAMEL框架 | 优势 |
|------|---------|----------|------|
| **开发时间** | 6-12个月 | 2-4周 | 🚀 **90% 时间节省** |
| **功能完整性** | 基础功能 | 企业级功能 | 📈 **10x 功能丰富度** |
| **稳定性** | 需要大量测试 | 学术验证 | 🛡️ **更高可靠性** |
| **工具生态** | 需要自建 | 开箱即用 | 🔧 **丰富工具包** |
| **协作模式** | 简单协作 | 多种模式 | 🤝 **专业协作** |
| **学术支持** | 无 | 顶级研究 | 🎓 **学术背书** |

### 技术优势

1. **成熟的智能体架构** - 基于学术研究的可靠框架
2. **丰富的协作模式** - RolePlaying、Workforce、Society等
3. **完整的工具生态** - 搜索、代码执行、数学计算等
4. **可观测性** - 完整的执行日志和状态监控
5. **扩展性** - 支持自定义智能体和工具包

### 业务优势

1. **快速上市** - 基于成熟框架，快速构建产品
2. **功能丰富** - 开箱即用的高级智能体功能
3. **质量保证** - 学术级别的代码质量和测试覆盖
4. **持续更新** - 活跃的开源社区和持续改进

## 🎯 下一步行动

### 立即开始（今天）

1. **运行初始化脚本**
   ```bash
   bash docs/rewrite/scripts/init-project.sh
   ```

2. **配置API密钥**
   ```bash
   # 编辑 .env.local
   OPENAI_API_KEY="your-api-key-here"
   ```

3. **启动开发服务器**
   ```bash
   pnpm dev
   ```

4. **体验CAMEL智能体**
   - 访问 http://localhost:3000
   - 创建第一个智能体
   - 设计第一个工作流
   - 观察智能体协作

### 深入学习（本周）

1. **阅读CAMEL文档** - [06-camel-integration.md](./06-camel-integration.md)
2. **学习开发指南** - [12-camel-development-guide.md](./12-camel-development-guide.md)
3. **查看代码示例** - 项目中的完整实现示例
4. **实践开发** - 创建自定义智能体和工作流

### 团队协作（下周）

1. **技术分享** - 向团队介绍CAMEL框架
2. **架构评审** - 讨论集成方案和实施计划
3. **开发规范** - 制定CAMEL智能体开发规范
4. **项目启动** - 正式开始基于CAMEL的开发

## 🎉 预期成果

通过集成CAMEL框架，SGASpace将获得：

### 技术成果
- **专业智能体能力** - 基于学术研究的可靠智能体框架
- **丰富协作模式** - 多种智能体协作模式，适应不同业务场景
- **完整工具生态** - 开箱即用的工具包，快速扩展功能
- **高质量代码** - 学术级别的代码质量和文档

### 业务成果
- **快速上市** - 基于成熟框架，大幅缩短开发时间
- **功能差异化** - 专业的多智能体协作，形成竞争优势
- **用户体验** - 智能化的任务处理，提升用户满意度
- **扩展能力** - 支持复杂业务场景，满足企业级需求

---

**开始您的CAMEL智能体之旅**: `bash docs/rewrite/scripts/init-project.sh`  
**文档版本**: 1.0.0  
**CAMEL版本**: 0.1.5+
