# SGASpace 系统架构设计

## 🏗️ 架构概览

SGASpace 采用**混合架构**设计，以 Next.js 15 为核心的 Web 应用层，配合独立的 CAMEL 智能体服务，通过 Redis 实现高性能的实时通信和任务调度。整体架构在保持开发简洁性的同时，确保各服务职责清晰、可独立扩展。

### 设计原则

1. **简化优先** - 避免过度工程化，专注核心业务价值
2. **服务分离** - 智能体服务独立部署，故障隔离
3. **类型安全** - 端到端 TypeScript，减少运行时错误
4. **渐进增强** - 从 MVP 开始，支持模块化扩展
5. **云原生** - 容器化部署，支持水平扩展

## 🎯 整体架构图

```mermaid
graph TB
    subgraph "客户端层"
        A[Web 浏览器]
        B[桌面应用 Electron]
    end

    subgraph "Web 应用层 - Next.js 15"
        C[App Router]
        D[React Server Components]
        E[tRPC API Routes]
        F[NextAuth 认证]
        G[多租户中间件]
    end

    subgraph "业务服务层"
        H[智能体管理服务]
        I[工作流引擎]
        J[知识库服务]
        K[实时通信服务]
    end

    subgraph "CAMEL 智能体服务 (Python)"
        L[FastAPI 网关]
        M[CAMEL ChatAgent]
        N[CAMEL RolePlaying]
        O[CAMEL Workforce]
        P[工具集成模块]
    end

    subgraph "数据存储层"
        Q[PostgreSQL 15<br/>+ pgvector]
        R[Redis 7<br/>缓存/队列/实时]
        S[MinIO/S3<br/>对象存储]
    end

    A --> C
    B --> C
    C --> D
    D --> E
    E --> F
    F --> G
    G --> H
    H --> I
    I --> J
    J --> K

    H --> L
    I --> L
    L --> M
    L --> N
    L --> O
    L --> P

    H --> Q
    I --> Q
    J --> Q
    K --> R
    I --> R
    J --> S
```

## 🔧 技术栈详解

### 前端技术栈

#### Next.js 15 App Router
```typescript
// app/layout.tsx - 根布局
export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN" suppressHydrationWarning>
      <body className={inter.className}>
        <Providers>
          <div className="min-h-screen bg-background">
            <Header />
            <div className="flex">
              <Sidebar />
              <main className="flex-1">
                {children}
              </main>
            </div>
          </div>
        </Providers>
      </body>
    </html>
  )
}
```

**选择理由**:
- 内置 SSR/SSG 支持，优化首屏加载
- 文件系统路由，简化路由管理
- React Server Components，减少客户端 JS
- 自动代码分割和优化

#### UI 组件库 - shadcn/ui
```typescript
// components/ui/button.tsx
import { cn } from "@/lib/utils"
import { Slot } from "@radix-ui/react-slot"

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
  size?: "default" | "sm" | "lg" | "icon"
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = "default", size = "default", asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
```

**技术选型**:
- **shadcn/ui**: 可定制的现代组件库
- **Radix UI**: 无障碍访问支持
- **Tailwind CSS**: 原子化 CSS，快速开发
- **React Flow**: 工作流可视化编辑器

### Web 应用层技术栈

#### API 设计 - tRPC
```typescript
// server/api/routers/agent.ts
export const agentRouter = createTRPCRouter({
  list: protectedProcedure
    .input(z.object({
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(100).default(20),
      type: z.enum(['task_planning', 'knowledge_management', 'decision_support']).optional(),
    }))
    .query(async ({ input, ctx }) => {
      const { page, limit, type } = input
      const skip = (page - 1) * limit

      const where = {
        tenantId: ctx.session.user.tenantId,
        ...(type && { type }),
      }

      const [agents, total] = await Promise.all([
        ctx.db.agent.findMany({
          where,
          skip,
          take: limit,
          include: {
            _count: {
              select: { workflows: true }
            }
          },
          orderBy: { createdAt: 'desc' }
        }),
        ctx.db.agent.count({ where })
      ])

      return {
        agents,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    }),

  // 调用 CAMEL 服务执行智能体
  execute: protectedProcedure
    .input(z.object({
      agentId: z.string(),
      input: z.any(),
      context: z.any().optional()
    }))
    .mutation(async ({ input, ctx }) => {
      // 调用独立的 CAMEL 服务
      const response = await fetch(`${process.env.CAMEL_SERVICE_URL}/agents/execute`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          agentId: input.agentId,
          input: input.input,
          context: input.context,
          tenantId: ctx.session.user.tenantId
        })
      })

      return await response.json()
    }),
})
```

**优势**:
- 端到端类型安全
- 自动生成客户端代码
- 内置输入验证和错误处理
- 优秀的开发体验

### CAMEL 智能体服务 (Python)

#### FastAPI 网关服务
```python
# camel_service/main.py
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from camel.agents import ChatAgent, RolePlaying
from camel.workforce import Workforce

app = FastAPI(title="SGASpace CAMEL Service")

class AgentExecuteRequest(BaseModel):
    agent_id: str
    input: dict
    context: dict = {}
    tenant_id: str

class AgentExecuteResponse(BaseModel):
    success: bool
    output: dict
    logs: list[str]
    tokens_used: int
    execution_time: float

@app.post("/agents/execute", response_model=AgentExecuteResponse)
async def execute_agent(request: AgentExecuteRequest):
    """执行智能体任务"""
    try:
        # 根据 agent_id 获取智能体配置
        agent_config = await get_agent_config(request.agent_id, request.tenant_id)

        # 创建 CAMEL 智能体实例
        agent = create_camel_agent(agent_config)

        # 执行任务
        result = await agent.step(request.input, context=request.context)

        return AgentExecuteResponse(
            success=True,
            output=result.output,
            logs=result.logs,
            tokens_used=result.token_usage,
            execution_time=result.execution_time
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/workflows/step")
async def execute_workflow_step(request: WorkflowStepRequest):
    """执行工作流步骤"""
    # 工作流步骤执行逻辑
    pass
```

#### 数据库设计 - Prisma + PostgreSQL
```prisma
// prisma/schema.prisma
model Agent {
  id          String      @id @default(cuid())
  name        String
  description String?
  type        AgentType
  config      Json        @default("{}")
  status      AgentStatus @default(INACTIVE)

  // 多租户 - 启用 RLS
  tenantId    String      @map("tenant_id")
  tenant      Tenant      @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // 创建者
  createdById String      @map("created_by_id")
  createdBy   User        @relation(fields: [createdById], references: [id])

  // 关联关系
  workflows   WorkflowAgent[]
  executions  AgentExecution[]

  createdAt   DateTime    @default(now()) @map("created_at")
  updatedAt   DateTime    @updatedAt @map("updated_at")

  @@map("agents")
  @@index([tenantId, type])
  @@index([tenantId, status])
}
```

### 数据存储架构

#### 主数据库 - PostgreSQL 15 + pgvector
```sql
-- 启用 pgvector 扩展
CREATE EXTENSION IF NOT EXISTS vector;

-- 租户隔离策略 (RLS)
ALTER TABLE agents ENABLE ROW LEVEL SECURITY;
CREATE POLICY tenant_isolation_agents ON agents
  FOR ALL TO authenticated_user
  USING (tenant_id = current_setting('app.current_tenant_id')::text);

-- 知识库向量表
CREATE TABLE document_chunks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id TEXT NOT NULL,
  document_id TEXT NOT NULL,
  chunk_index INTEGER NOT NULL,
  content TEXT NOT NULL,
  embedding vector(1536), -- OpenAI embedding 维度
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW()
);

-- 向量相似度索引
CREATE INDEX ON document_chunks USING ivfflat (embedding vector_cosine_ops);

-- 性能优化索引
CREATE INDEX CONCURRENTLY idx_agents_tenant_type ON agents(tenant_id, type);
CREATE INDEX CONCURRENTLY idx_workflows_tenant_status ON workflows(tenant_id, status);
CREATE INDEX CONCURRENTLY idx_document_chunks_tenant ON document_chunks(tenant_id);
```

#### Redis 多功能层
```typescript
// lib/redis.ts
export class RedisService {
  private redis: Redis
  private publisher: Redis
  private subscriber: Redis

  constructor() {
    this.redis = new Redis(process.env.REDIS_URL!)
    this.publisher = new Redis(process.env.REDIS_URL!)
    this.subscriber = new Redis(process.env.REDIS_URL!)
  }

  // 1. 缓存服务
  async cacheAgentStatus(agentId: string, status: AgentStatus, ttl = 300) {
    const key = `agent:status:${agentId}`
    await this.redis.setex(key, ttl, JSON.stringify(status))
  }

  // 2. 任务队列 (BullMQ)
  async enqueueWorkflowExecution(workflowId: string, input: any) {
    const queue = new Queue('workflow-execution', { connection: this.redis })
    await queue.add('execute', { workflowId, input })
  }

  // 3. 实时通信 (Pub/Sub)
  async publishWorkflowStatus(tenantId: string, workflowId: string, status: any) {
    const channel = `tenant:${tenantId}:workflow:${workflowId}`
    await this.publisher.publish(channel, JSON.stringify(status))
  }

  async subscribeToTenantEvents(tenantId: string, callback: (message: any) => void) {
    const pattern = `tenant:${tenantId}:*`
    this.subscriber.psubscribe(pattern)
    this.subscriber.on('pmessage', (pattern, channel, message) => {
      callback(JSON.parse(message))
    })
  }
}
```

#### 知识库服务 - pgvector
```typescript
// lib/knowledge-base.ts
export class KnowledgeBaseService {
  private db: PrismaClient
  private openai: OpenAI

  constructor() {
    this.db = new PrismaClient()
    this.openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY })
  }

  // 文档向量化处理
  async indexDocument(tenantId: string, document: Document) {
    const chunks = await this.chunkDocument(document.content)

    const chunkData = await Promise.all(
      chunks.map(async (chunk, index) => {
        const embedding = await this.createEmbedding(chunk.content)

        return {
          tenantId,
          documentId: document.id,
          chunkIndex: index,
          content: chunk.content,
          embedding: `[${embedding.join(',')}]`, // pgvector 格式
          metadata: chunk.metadata
        }
      })
    )

    // 批量插入向量数据
    await this.db.$executeRaw`
      INSERT INTO document_chunks (tenant_id, document_id, chunk_index, content, embedding, metadata)
      VALUES ${Prisma.join(chunkData.map(chunk =>
        Prisma.sql`(${chunk.tenantId}, ${chunk.documentId}, ${chunk.chunkIndex}, ${chunk.content}, ${chunk.embedding}::vector, ${chunk.metadata}::jsonb)`
      ))}
    `
  }

  // 语义检索
  async searchSimilar(tenantId: string, query: string, limit = 10) {
    const queryEmbedding = await this.createEmbedding(query)
    const embeddingVector = `[${queryEmbedding.join(',')}]`

    const results = await this.db.$queryRaw`
      SELECT
        content,
        metadata,
        1 - (embedding <=> ${embeddingVector}::vector) as similarity
      FROM document_chunks
      WHERE tenant_id = ${tenantId}
        AND 1 - (embedding <=> ${embeddingVector}::vector) > 0.7
      ORDER BY embedding <=> ${embeddingVector}::vector
      LIMIT ${limit}
    `

    return results
  }

  private async createEmbedding(text: string): Promise<number[]> {
    const response = await this.openai.embeddings.create({
      model: 'text-embedding-3-small',
      input: text
    })
    return response.data[0].embedding
  }
}
```

## 🔐 安全架构

### 认证授权 - NextAuth.js
```typescript
// lib/auth.ts
export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) return null

        const user = await verifyUser(credentials.email, credentials.password)
        if (!user) return null

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          tenantId: user.tenantId,
          role: user.role
        }
      }
    })
  ],
  session: { strategy: "jwt" },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.tenantId = user.tenantId
        token.role = user.role
      }
      return token
    },
    async session({ session, token }) {
      session.user.id = token.sub!
      session.user.tenantId = token.tenantId as string
      session.user.role = token.role as string
      return session
    }
  }
}
```

### 多租户安全中间件
```typescript
// middleware.ts
export async function middleware(request: NextRequest) {
  // API 路由租户上下文注入
  if (request.nextUrl.pathname.startsWith('/api/')) {
    const session = await getToken({ req: request })
    if (!session?.tenantId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // 注入租户上下文到数据库连接
    request.headers.set('x-tenant-id', session.tenantId)
    request.headers.set('x-user-id', session.sub!)
  }

  return NextResponse.next()
}

// Prisma 中间件强制租户隔离
export const prismaWithTenant = (tenantId: string) => {
  return prisma.$extends({
    query: {
      $allModels: {
        async $allOperations({ model, operation, args, query }) {
          // 强制注入 tenantId 过滤条件
          if (args.where) {
            args.where = { ...args.where, tenantId }
          } else {
            args.where = { tenantId }
          }
          return query(args)
        }
      }
    }
  })
}
```

## 📊 实时通信架构

### Redis Pub/Sub 实时服务
```typescript
// lib/realtime.ts
export class RealtimeService {
  private redis: Redis
  private publisher: Redis
  private subscriber: Redis

  constructor() {
    this.redis = new Redis(process.env.REDIS_URL!)
    this.publisher = new Redis(process.env.REDIS_URL!)
    this.subscriber = new Redis(process.env.REDIS_URL!)
  }

  // 发布工作流状态更新
  async publishWorkflowStatus(tenantId: string, workflowId: string, status: any) {
    const channel = `tenant:${tenantId}:workflow:${workflowId}`
    const message = {
      type: 'workflow-status',
      workflowId,
      status,
      timestamp: Date.now()
    }

    await this.publisher.publish(channel, JSON.stringify(message))
  }

  // 发布智能体执行状态
  async publishAgentStatus(tenantId: string, agentId: string, status: any) {
    const channel = `tenant:${tenantId}:agent:${agentId}`
    const message = {
      type: 'agent-status',
      agentId,
      status,
      timestamp: Date.now()
    }

    await this.publisher.publish(channel, JSON.stringify(message))
  }

  // 订阅租户事件
  async subscribeToTenant(tenantId: string, callback: (message: any) => void) {
    const pattern = `tenant:${tenantId}:*`
    await this.subscriber.psubscribe(pattern)

    this.subscriber.on('pmessage', (pattern, channel, message) => {
      try {
        const data = JSON.parse(message)
        callback(data)
      } catch (error) {
        console.error('Failed to parse realtime message:', error)
      }
    })
  }
}

// 前端 WebSocket 客户端
export class RealtimeClient {
  private ws: WebSocket | null = null
  private tenantId: string

  constructor(tenantId: string) {
    this.tenantId = tenantId
    this.connect()
  }

  private connect() {
    this.ws = new WebSocket(`${process.env.NEXT_PUBLIC_WS_URL}?tenantId=${this.tenantId}`)

    this.ws.onopen = () => {
      console.log('Realtime connection established')
    }

    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data)
      this.handleMessage(data)
    }
  }

  private handleMessage(data: any) {
    // 分发到相应的事件处理器
    switch (data.type) {
      case 'workflow-status':
        this.onWorkflowStatus?.(data)
        break
      case 'agent-status':
        this.onAgentStatus?.(data)
        break
    }
  }

  onWorkflowStatus?: (data: any) => void
  onAgentStatus?: (data: any) => void
}
```

## 🚀 部署架构

### 开发环境 (docker-compose)
```yaml
# docker-compose.yml
version: '3.8'
services:
  web:
    build: .
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=********************************************/sgaspace
      - REDIS_URL=redis://redis:6379
      - CAMEL_SERVICE_URL=http://camel-service:8000
    depends_on:
      - postgres
      - redis
      - camel-service

  camel-service:
    build: ./camel_service
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=********************************************/sgaspace
    depends_on:
      - postgres

  postgres:
    image: pgvector/pgvector:pg15
    environment:
      - POSTGRES_DB=sgaspace
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

volumes:
  postgres_data:
```

---

**文档版本**: 2.0.0
**最后更新**: 2024-12-19
**架构负责人**: 技术团队
