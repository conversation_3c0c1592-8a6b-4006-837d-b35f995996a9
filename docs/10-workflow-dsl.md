# SGASpace 工作流 DSL 规范

## 🔧 DSL 概览

SGASpace 工作流 DSL (Domain Specific Language) 是一套用于定义智能体协作工作流的声明式语言规范。它基于 JSON 格式，支持可视化编辑器和代码编辑器双重编辑模式。

### 设计原则

1. **声明式** - 描述"做什么"而不是"怎么做"
2. **类型安全** - 完整的 TypeScript 类型定义
3. **可扩展** - 支持自定义节点类型和插件
4. **可读性** - 人类可读的 JSON 结构
5. **版本化** - 支持向后兼容的版本演进

## 📋 DSL 结构定义

### 工作流定义

```typescript
interface WorkflowDefinition {
  // 元数据
  id: string
  name: string
  description?: string
  version: string
  
  // DSL版本
  dslVersion: '1.0'
  
  // 节点和连接
  nodes: WorkflowNode[]
  edges: WorkflowEdge[]
  
  // 全局配置
  variables: WorkflowVariable[]
  settings: WorkflowSettings
  
  // 元信息
  metadata: WorkflowMetadata
}
```

### 节点定义

```typescript
interface WorkflowNode {
  id: string
  type: NodeType
  position: { x: number; y: number }
  data: NodeData
  config: NodeConfig
  
  // 可选配置
  retry?: RetryConfig
  timeout?: number // 超时时间（秒）
  condition?: ConditionExpression // 执行条件
}

enum NodeType {
  START = 'start',
  END = 'end',
  AGENT = 'agent',
  CONDITION = 'condition',
  LOOP = 'loop',
  PARALLEL = 'parallel',
  TOOL = 'tool',
  DATA = 'data',
  DELAY = 'delay',
  WEBHOOK = 'webhook',
  HUMAN = 'human' // 人工审核节点
}
```

### 边连接定义

```typescript
interface WorkflowEdge {
  id: string
  source: string // 源节点ID
  target: string // 目标节点ID
  
  // 连接点
  sourceHandle?: string
  targetHandle?: string
  
  // 条件执行
  condition?: EdgeCondition
  label?: string
  
  // 样式配置
  style?: EdgeStyle
}

interface EdgeCondition {
  type: 'expression' | 'value' | 'always'
  expression?: string // JavaScript表达式
  value?: any // 期望值
}
```

## 🎯 节点类型详解

### 1. 智能体节点 (Agent)

```typescript
interface AgentNodeData {
  agentId: string
  agentType: 'TASK_PLANNING' | 'KNOWLEDGE_MANAGEMENT' | 'DECISION_SUPPORT' | 'TOOL_INTEGRATION'
  
  // 输入配置
  input: {
    message?: string | VariableReference
    context?: Record<string, any>
    parameters?: Record<string, any>
  }
  
  // 输出映射
  output?: {
    mapping: Record<string, string> // 输出字段映射
    transform?: string // 输出转换表达式
  }
}

// 示例
{
  "id": "agent_1",
  "type": "agent",
  "position": { "x": 100, "y": 100 },
  "data": {
    "agentId": "task_planner_001",
    "agentType": "TASK_PLANNING",
    "input": {
      "message": "{{workflow.input.task_description}}",
      "context": {
        "priority": "high",
        "deadline": "{{variables.deadline}}"
      }
    },
    "output": {
      "mapping": {
        "plan": "execution_plan",
        "steps": "task_steps"
      }
    }
  },
  "config": {
    "timeout": 60,
    "retry": {
      "maxAttempts": 3,
      "backoffType": "exponential",
      "initialDelay": 1000
    }
  }
}
```

### 2. 条件节点 (Condition)

```typescript
interface ConditionNodeData {
  conditions: ConditionRule[]
  operator: 'AND' | 'OR' // 多条件逻辑
  
  // 分支配置
  branches: {
    true: string // 条件为真时的下一个节点
    false: string // 条件为假时的下一个节点
  }
}

interface ConditionRule {
  field: string | VariableReference
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'contains' | 'startsWith' | 'endsWith' | 'regex'
  value: any
  logicalOperator?: 'AND' | 'OR'
}

// 示例
{
  "id": "condition_1",
  "type": "condition",
  "data": {
    "conditions": [
      {
        "field": "{{nodes.agent_1.output.confidence}}",
        "operator": "gte",
        "value": 0.8
      },
      {
        "field": "{{nodes.agent_1.output.status}}",
        "operator": "eq",
        "value": "success",
        "logicalOperator": "AND"
      }
    ],
    "operator": "AND",
    "branches": {
      "true": "agent_2",
      "false": "human_review"
    }
  }
}
```

### 3. 循环节点 (Loop)

```typescript
interface LoopNodeData {
  loopType: 'for' | 'while' | 'forEach'
  
  // for循环配置
  forConfig?: {
    start: number
    end: number
    step: number
    variable: string // 循环变量名
  }
  
  // while循环配置
  whileConfig?: {
    condition: ConditionRule[]
    maxIterations: number
  }
  
  // forEach循环配置
  forEachConfig?: {
    array: string | VariableReference
    itemVariable: string
    indexVariable?: string
  }
  
  // 循环体节点
  bodyNodes: string[] // 循环体内的节点ID列表
}

// 示例：forEach循环
{
  "id": "loop_1",
  "type": "loop",
  "data": {
    "loopType": "forEach",
    "forEachConfig": {
      "array": "{{nodes.agent_1.output.task_list}}",
      "itemVariable": "current_task",
      "indexVariable": "task_index"
    },
    "bodyNodes": ["agent_2", "condition_1"]
  }
}
```

### 4. 并行节点 (Parallel)

```typescript
interface ParallelNodeData {
  branches: ParallelBranch[]
  joinType: 'all' | 'any' | 'first' // 等待策略
  timeout?: number // 并行执行超时
}

interface ParallelBranch {
  id: string
  name: string
  nodes: string[] // 分支内的节点ID列表
  condition?: ConditionExpression // 分支执行条件
}

// 示例
{
  "id": "parallel_1",
  "type": "parallel",
  "data": {
    "branches": [
      {
        "id": "branch_a",
        "name": "数据分析分支",
        "nodes": ["agent_data_analyst"]
      },
      {
        "id": "branch_b", 
        "name": "内容生成分支",
        "nodes": ["agent_content_generator"]
      }
    ],
    "joinType": "all",
    "timeout": 300
  }
}
```

### 5. 工具节点 (Tool)

```typescript
interface ToolNodeData {
  toolName: string
  toolConfig: Record<string, any>
  
  // 输入输出映射
  inputMapping?: Record<string, string>
  outputMapping?: Record<string, string>
}

// 示例：HTTP请求工具
{
  "id": "tool_1",
  "type": "tool",
  "data": {
    "toolName": "http_request",
    "toolConfig": {
      "method": "POST",
      "url": "https://api.example.com/data",
      "headers": {
        "Content-Type": "application/json",
        "Authorization": "Bearer {{variables.api_token}}"
      },
      "body": {
        "data": "{{nodes.agent_1.output.processed_data}}"
      }
    },
    "outputMapping": {
      "response": "api_response",
      "status": "http_status"
    }
  }
}
```

## 🔗 变量引用系统

### 变量引用语法

```typescript
type VariableReference = string // 格式: {{scope.path}}

// 支持的作用域
interface VariableScopes {
  // 工作流输入
  'workflow.input': any
  
  // 节点输出
  'nodes.{nodeId}.output': any
  
  // 全局变量
  'variables.{variableName}': any
  
  // 循环变量
  'loop.{variableName}': any
  
  // 系统变量
  'system.timestamp': number
  'system.executionId': string
  'system.tenantId': string
  'system.userId': string
}
```

### 变量引用示例

```json
{
  "message": "处理任务：{{workflow.input.task_name}}",
  "context": {
    "previous_result": "{{nodes.agent_1.output.result}}",
    "current_time": "{{system.timestamp}}",
    "user_preference": "{{variables.user_settings.language}}"
  }
}
```

## ⚙️ 配置选项

### 重试配置

```typescript
interface RetryConfig {
  maxAttempts: number // 最大重试次数
  backoffType: 'fixed' | 'exponential' | 'linear'
  initialDelay: number // 初始延迟（毫秒）
  maxDelay?: number // 最大延迟
  retryConditions?: string[] // 重试条件表达式
}
```

### 工作流设置

```typescript
interface WorkflowSettings {
  // 执行配置
  execution: {
    timeout: number // 总超时时间
    maxConcurrency: number // 最大并发数
    errorHandling: 'stop' | 'continue' | 'retry'
  }
  
  // 日志配置
  logging: {
    level: 'debug' | 'info' | 'warn' | 'error'
    includeInputOutput: boolean
    retentionDays: number
  }
  
  // 通知配置
  notifications: {
    onSuccess?: NotificationConfig
    onFailure?: NotificationConfig
    onTimeout?: NotificationConfig
  }
}
```

## 📝 完整示例

```json
{
  "id": "workflow_001",
  "name": "智能内容生成工作流",
  "description": "基于用户需求生成个性化内容",
  "version": "1.0.0",
  "dslVersion": "1.0",
  
  "variables": [
    {
      "name": "content_type",
      "type": "string",
      "defaultValue": "article",
      "description": "内容类型"
    },
    {
      "name": "target_audience",
      "type": "string",
      "description": "目标受众"
    }
  ],
  
  "nodes": [
    {
      "id": "start",
      "type": "start",
      "position": { "x": 0, "y": 0 },
      "data": {}
    },
    {
      "id": "task_planner",
      "type": "agent",
      "position": { "x": 200, "y": 0 },
      "data": {
        "agentId": "task_planner_001",
        "agentType": "TASK_PLANNING",
        "input": {
          "message": "为{{variables.target_audience}}创建{{variables.content_type}}的详细计划",
          "context": {
            "requirements": "{{workflow.input.requirements}}"
          }
        }
      },
      "config": {
        "timeout": 60,
        "retry": {
          "maxAttempts": 3,
          "backoffType": "exponential",
          "initialDelay": 1000
        }
      }
    },
    {
      "id": "quality_check",
      "type": "condition",
      "position": { "x": 400, "y": 0 },
      "data": {
        "conditions": [
          {
            "field": "{{nodes.task_planner.output.confidence}}",
            "operator": "gte",
            "value": 0.7
          }
        ],
        "branches": {
          "true": "content_generator",
          "false": "human_review"
        }
      }
    },
    {
      "id": "content_generator",
      "type": "agent",
      "position": { "x": 600, "y": -100 },
      "data": {
        "agentId": "content_gen_001",
        "agentType": "KNOWLEDGE_MANAGEMENT",
        "input": {
          "message": "根据计划生成内容：{{nodes.task_planner.output.plan}}",
          "context": {
            "style": "professional",
            "length": "medium"
          }
        }
      }
    },
    {
      "id": "human_review",
      "type": "human",
      "position": { "x": 600, "y": 100 },
      "data": {
        "title": "人工审核",
        "description": "请审核任务规划结果",
        "assignee": "{{workflow.input.reviewer}}",
        "fields": [
          {
            "name": "approved",
            "type": "boolean",
            "label": "是否批准"
          },
          {
            "name": "feedback",
            "type": "text",
            "label": "反馈意见"
          }
        ]
      }
    },
    {
      "id": "end",
      "type": "end",
      "position": { "x": 800, "y": 0 },
      "data": {
        "output": {
          "content": "{{nodes.content_generator.output.content}}",
          "metadata": {
            "generated_at": "{{system.timestamp}}",
            "quality_score": "{{nodes.task_planner.output.confidence}}"
          }
        }
      }
    }
  ],
  
  "edges": [
    {
      "id": "e1",
      "source": "start",
      "target": "task_planner"
    },
    {
      "id": "e2", 
      "source": "task_planner",
      "target": "quality_check"
    },
    {
      "id": "e3",
      "source": "quality_check",
      "target": "content_generator",
      "condition": {
        "type": "value",
        "value": true
      }
    },
    {
      "id": "e4",
      "source": "quality_check", 
      "target": "human_review",
      "condition": {
        "type": "value",
        "value": false
      }
    },
    {
      "id": "e5",
      "source": "content_generator",
      "target": "end"
    },
    {
      "id": "e6",
      "source": "human_review",
      "target": "end",
      "condition": {
        "type": "expression",
        "expression": "{{nodes.human_review.output.approved}} === true"
      }
    }
  ],
  
  "settings": {
    "execution": {
      "timeout": 1800,
      "maxConcurrency": 5,
      "errorHandling": "stop"
    },
    "logging": {
      "level": "info",
      "includeInputOutput": true,
      "retentionDays": 30
    }
  },
  
  "metadata": {
    "createdAt": "2024-12-19T10:00:00Z",
    "createdBy": "user_001",
    "tags": ["content", "ai", "automation"],
    "category": "content_generation"
  }
}
```

---

**文档版本**: 1.0.0  
**最后更新**: 2024-12-19  
**DSL版本**: v1.0
