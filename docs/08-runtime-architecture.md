# SGASpace 运行时架构设计

## 🏗️ 运行时架构概览

SGASpace 采用**微服务化的运行时架构**，将不同职责的服务进行分离，确保系统的可扩展性、可维护性和故障隔离。

### 核心设计原则

1. **服务分离** - Web应用与AI服务独立部署
2. **故障隔离** - 单个服务故障不影响整体系统
3. **水平扩展** - 各服务可根据负载独立扩缩容
4. **状态管理** - 集中式状态管理与分布式缓存
5. **可观测性** - 全链路监控与日志追踪

## 🎯 服务拓扑图

```mermaid
graph TB
    subgraph "负载均衡层"
        LB[Nginx/ALB]
    end
    
    subgraph "Web应用层"
        WEB1[Next.js App 1]
        WEB2[Next.js App 2]
        WEB3[Next.js App 3]
    end
    
    subgraph "AI服务层"
        CAMEL1[CAMEL Service 1]
        CAMEL2[CAMEL Service 2]
    end
    
    subgraph "数据层"
        PG[(PostgreSQL<br/>主从复制)]
        REDIS[(Redis Cluster<br/>缓存/队列/实时)]
        S3[(MinIO/S3<br/>对象存储)]
    end
    
    subgraph "监控层"
        OTEL[OpenTelemetry]
        PROM[Prometheus]
        GRAF[Grafana]
    end
    
    LB --> WEB1
    LB --> WEB2
    LB --> WEB3
    
    WEB1 --> CAMEL1
    WEB2 --> CAMEL1
    WEB3 --> CAMEL2
    
    WEB1 --> PG
    WEB2 --> PG
    WEB3 --> PG
    
    WEB1 --> REDIS
    WEB2 --> REDIS
    WEB3 --> REDIS
    
    CAMEL1 --> PG
    CAMEL2 --> PG
    
    WEB1 --> S3
    WEB2 --> S3
    WEB3 --> S3
    
    WEB1 --> OTEL
    WEB2 --> OTEL
    WEB3 --> OTEL
    CAMEL1 --> OTEL
    CAMEL2 --> OTEL
    
    OTEL --> PROM
    PROM --> GRAF
```

## 🐳 容器化部署

### Docker Compose 开发环境

```yaml
# docker-compose.yml
version: '3.8'

services:
  # Web 应用
  web:
    build: 
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=********************************************/sgaspace
      - REDIS_URL=redis://redis:6379
      - CAMEL_SERVICE_URL=http://camel-service:8000
      - NEXTAUTH_SECRET=your-secret-key
      - NEXTAUTH_URL=http://localhost:3000
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    depends_on:
      - postgres
      - redis
      - camel-service
    networks:
      - sgaspace-network
    volumes:
      - .:/app
      - /app/node_modules
    restart: unless-stopped

  # CAMEL 智能体服务
  camel-service:
    build: 
      context: ./camel_service
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - DATABASE_URL=********************************************/sgaspace
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    networks:
      - sgaspace-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL 数据库
  postgres:
    image: pgvector/pgvector:pg15
    environment:
      - POSTGRES_DB=sgaspace
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - sgaspace-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存/队列/实时
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    networks:
      - sgaspace-network
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # MinIO 对象存储
  minio:
    image: minio/minio:latest
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin123
    volumes:
      - minio_data:/data
    networks:
      - sgaspace-network
    command: server /data --console-address ":9001"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - web
    networks:
      - sgaspace-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  minio_data:

networks:
  sgaspace-network:
    driver: bridge
```

### Dockerfile 配置

```dockerfile
# Dockerfile (Next.js Web应用)
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml* ./
RUN \
  if [ -f yarn.lock ]; then yarn --frozen-lockfile; \
  elif [ -f package-lock.json ]; then npm ci; \
  elif [ -f pnpm-lock.yaml ]; then yarn global add pnpm && pnpm i --frozen-lockfile; \
  else echo "Lockfile not found." && exit 1; \
  fi

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Generate Prisma client
RUN npx prisma generate

# Build the application
RUN yarn build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

```dockerfile
# camel_service/Dockerfile (CAMEL服务)
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建非root用户
RUN useradd --create-home --shell /bin/bash app \
    && chown -R app:app /app
USER app

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

## ⚙️ 生产环境部署

### Kubernetes 部署配置

```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: sgaspace
---
# k8s/web-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sgaspace-web
  namespace: sgaspace
spec:
  replicas: 3
  selector:
    matchLabels:
      app: sgaspace-web
  template:
    metadata:
      labels:
        app: sgaspace-web
    spec:
      containers:
      - name: web
        image: sgaspace/web:latest
        ports:
        - containerPort: 3000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: sgaspace-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: sgaspace-secrets
              key: redis-url
        - name: CAMEL_SERVICE_URL
          value: "http://sgaspace-camel-service:8000"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: sgaspace-web-service
  namespace: sgaspace
spec:
  selector:
    app: sgaspace-web
  ports:
  - protocol: TCP
    port: 80
    targetPort: 3000
  type: ClusterIP
---
# k8s/camel-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sgaspace-camel
  namespace: sgaspace
spec:
  replicas: 2
  selector:
    matchLabels:
      app: sgaspace-camel
  template:
    metadata:
      labels:
        app: sgaspace-camel
    spec:
      containers:
      - name: camel
        image: sgaspace/camel:latest
        ports:
        - containerPort: 8000
        env:
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: sgaspace-secrets
              key: openai-api-key
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: sgaspace-secrets
              key: database-url
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 10
          periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: sgaspace-camel-service
  namespace: sgaspace
spec:
  selector:
    app: sgaspace-camel
  ports:
  - protocol: TCP
    port: 8000
    targetPort: 8000
  type: ClusterIP
```

### Nginx 配置

```nginx
# nginx.conf
events {
    worker_connections 1024;
}

http {
    upstream web_backend {
        server web:3000;
    }

    upstream camel_backend {
        server camel-service:8000;
    }

    # 限流配置
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=camel:10m rate=5r/s;

    server {
        listen 80;
        server_name localhost;

        # 安全头
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";

        # Web应用
        location / {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://web_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # WebSocket支持
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }

        # CAMEL API
        location /api/camel/ {
            limit_req zone=camel burst=10 nodelay;
            proxy_pass http://camel_backend/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # 超时配置
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        # 静态资源缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
}
```

## 📊 监控与可观测性

### OpenTelemetry 配置

```typescript
// lib/telemetry.ts
import { NodeSDK } from '@opentelemetry/sdk-node'
import { Resource } from '@opentelemetry/resources'
import { SemanticResourceAttributes } from '@opentelemetry/semantic-conventions'
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node'
import { PeriodicExportingMetricReader } from '@opentelemetry/sdk-metrics'
import { PrometheusExporter } from '@opentelemetry/exporter-prometheus'

const sdk = new NodeSDK({
  resource: new Resource({
    [SemanticResourceAttributes.SERVICE_NAME]: 'sgaspace-web',
    [SemanticResourceAttributes.SERVICE_VERSION]: process.env.APP_VERSION || '1.0.0',
    [SemanticResourceAttributes.DEPLOYMENT_ENVIRONMENT]: process.env.NODE_ENV || 'development',
  }),
  instrumentations: [getNodeAutoInstrumentations({
    '@opentelemetry/instrumentation-fs': {
      enabled: false, // 避免文件系统噪音
    },
  })],
  metricReader: new PeriodicExportingMetricReader({
    exporter: new PrometheusExporter({
      port: 9464,
    }),
    exportIntervalMillis: 5000,
  }),
})

// 初始化SDK
sdk.start()

// 优雅关闭
process.on('SIGTERM', () => {
  sdk.shutdown()
    .then(() => console.log('Telemetry terminated'))
    .catch((error) => console.log('Error terminating telemetry', error))
    .finally(() => process.exit(0))
})
```

### Prometheus 监控指标

```typescript
// lib/metrics.ts
import { metrics } from '@opentelemetry/api'

const meter = metrics.getMeter('sgaspace-web')

// 业务指标
export const workflowExecutions = meter.createCounter('workflow_executions_total', {
  description: 'Total number of workflow executions'
})

export const agentExecutions = meter.createCounter('agent_executions_total', {
  description: 'Total number of agent executions'
})

export const workflowDuration = meter.createHistogram('workflow_duration_seconds', {
  description: 'Workflow execution duration in seconds'
})

export const agentDuration = meter.createHistogram('agent_duration_seconds', {
  description: 'Agent execution duration in seconds'
})

export const activeUsers = meter.createUpDownCounter('active_users', {
  description: 'Number of active users'
})

export const queueSize = meter.createUpDownCounter('queue_size', {
  description: 'Number of jobs in queue'
})

// 使用示例
export function recordWorkflowExecution(tenantId: string, workflowId: string, duration: number, status: string) {
  workflowExecutions.add(1, {
    tenant_id: tenantId,
    workflow_id: workflowId,
    status
  })

  workflowDuration.record(duration, {
    tenant_id: tenantId,
    workflow_id: workflowId,
    status
  })
}
```

### 健康检查端点

```typescript
// pages/api/health.ts
import type { NextApiRequest, NextApiResponse } from 'next'
import { prisma } from '@/lib/prisma'
import { Redis } from 'ioredis'

interface HealthStatus {
  status: 'healthy' | 'unhealthy'
  timestamp: string
  services: {
    database: 'up' | 'down'
    redis: 'up' | 'down'
    camel: 'up' | 'down'
  }
  version: string
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<HealthStatus>
) {
  const timestamp = new Date().toISOString()
  const version = process.env.APP_VERSION || '1.0.0'

  const services = {
    database: 'down' as const,
    redis: 'down' as const,
    camel: 'down' as const
  }

  // 检查数据库
  try {
    await prisma.$queryRaw`SELECT 1`
    services.database = 'up'
  } catch (error) {
    console.error('Database health check failed:', error)
  }

  // 检查Redis
  try {
    const redis = new Redis(process.env.REDIS_URL!)
    await redis.ping()
    await redis.disconnect()
    services.redis = 'up'
  } catch (error) {
    console.error('Redis health check failed:', error)
  }

  // 检查CAMEL服务
  try {
    const response = await fetch(`${process.env.CAMEL_SERVICE_URL}/health`, {
      timeout: 5000
    })
    if (response.ok) {
      services.camel = 'up'
    }
  } catch (error) {
    console.error('CAMEL service health check failed:', error)
  }

  const allServicesUp = Object.values(services).every(status => status === 'up')
  const status = allServicesUp ? 'healthy' : 'unhealthy'

  res.status(allServicesUp ? 200 : 503).json({
    status,
    timestamp,
    services,
    version
  })
}
```

## 🔧 环境配置管理

### 环境变量配置

```bash
# .env.example
# 数据库配置
DATABASE_URL="postgresql://postgres:password@localhost:5432/sgaspace"

# Redis配置
REDIS_URL="redis://localhost:6379"

# CAMEL服务配置
CAMEL_SERVICE_URL="http://localhost:8000"

# NextAuth配置
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"

# OpenAI配置
OPENAI_API_KEY="your-openai-api-key"

# 对象存储配置
S3_ENDPOINT="http://localhost:9000"
S3_ACCESS_KEY="minioadmin"
S3_SECRET_KEY="minioadmin123"
S3_BUCKET="sgaspace"

# 监控配置
OTEL_EXPORTER_OTLP_ENDPOINT="http://localhost:4317"
PROMETHEUS_PORT="9464"

# 应用配置
APP_VERSION="1.0.0"
LOG_LEVEL="info"
```

### Docker Compose 覆盖配置

```yaml
# docker-compose.override.yml (开发环境)
version: '3.8'

services:
  web:
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - LOG_LEVEL=debug
    command: npm run dev

  camel-service:
    volumes:
      - ./camel_service:/app
    environment:
      - LOG_LEVEL=debug
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

```yaml
# docker-compose.prod.yml (生产环境)
version: '3.8'

services:
  web:
    environment:
      - NODE_ENV=production
      - LOG_LEVEL=info
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  camel-service:
    environment:
      - LOG_LEVEL=info
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  postgres:
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  redis:
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
```

## 🚀 部署脚本

### 自动化部署脚本

```bash
#!/bin/bash
# deploy.sh

set -e

# 配置
ENVIRONMENT=${1:-development}
VERSION=${2:-latest}

echo "🚀 Deploying SGASpace to $ENVIRONMENT environment..."

# 构建镜像
echo "📦 Building Docker images..."
docker build -t sgaspace/web:$VERSION .
docker build -t sgaspace/camel:$VERSION ./camel_service

# 推送到镜像仓库（生产环境）
if [ "$ENVIRONMENT" = "production" ]; then
    echo "📤 Pushing images to registry..."
    docker push sgaspace/web:$VERSION
    docker push sgaspace/camel:$VERSION
fi

# 部署
case $ENVIRONMENT in
    "development")
        echo "🔧 Starting development environment..."
        docker-compose up -d
        ;;
    "staging")
        echo "🧪 Deploying to staging..."
        docker-compose -f docker-compose.yml -f docker-compose.staging.yml up -d
        ;;
    "production")
        echo "🏭 Deploying to production..."
        kubectl apply -f k8s/
        kubectl set image deployment/sgaspace-web web=sgaspace/web:$VERSION -n sgaspace
        kubectl set image deployment/sgaspace-camel camel=sgaspace/camel:$VERSION -n sgaspace
        kubectl rollout status deployment/sgaspace-web -n sgaspace
        kubectl rollout status deployment/sgaspace-camel -n sgaspace
        ;;
    *)
        echo "❌ Unknown environment: $ENVIRONMENT"
        exit 1
        ;;
esac

echo "✅ Deployment completed successfully!"

# 健康检查
echo "🔍 Performing health checks..."
sleep 30

if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
    echo "✅ Web service is healthy"
else
    echo "❌ Web service health check failed"
    exit 1
fi

if curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ CAMEL service is healthy"
else
    echo "❌ CAMEL service health check failed"
    exit 1
fi

echo "🎉 All services are running and healthy!"
```

---

**文档版本**: 1.0.0
**最后更新**: 2024-12-19
**运行时架构版本**: v1.0
