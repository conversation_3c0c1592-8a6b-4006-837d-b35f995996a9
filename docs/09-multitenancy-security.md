# SGASpace 多租户安全设计

## 🔐 多租户安全概览

SGASpace 采用**深度防御**的多租户安全策略，通过数据库行级安全(RLS)、应用层中间件、API权限控制等多层防护，确保租户数据的完全隔离和安全。

### 安全设计原则

1. **零信任架构** - 不信任任何请求，每次都验证
2. **深度防御** - 多层安全控制，单点失效不影响整体安全
3. **最小权限** - 用户和服务只获得必需的最小权限
4. **数据隔离** - 租户数据在数据库层面强制隔离
5. **审计追踪** - 所有操作都有完整的审计日志

## 🏗️ 多租户架构图

```mermaid
graph TB
    subgraph "客户端层"
        USER[用户请求]
    end
    
    subgraph "认证层"
        AUTH[NextAuth.js]
        JWT[JWT Token]
        SESSION[Session管理]
    end
    
    subgraph "授权层"
        RBAC[角色权限控制]
        TENANT[租户上下文]
        MIDDLEWARE[Prisma中间件]
    end
    
    subgraph "数据库层"
        RLS[Row Level Security]
        POLICY[安全策略]
        AUDIT[审计日志]
    end
    
    subgraph "应用层"
        TRPC[tRPC API]
        CAMEL[CAMEL服务]
        REDIS[Redis缓存]
    end
    
    USER --> AUTH
    AUTH --> JWT
    JWT --> SESSION
    SESSION --> RBAC
    RBAC --> TENANT
    TENANT --> MIDDLEWARE
    MIDDLEWARE --> RLS
    RLS --> POLICY
    POLICY --> AUDIT
    
    TRPC --> MIDDLEWARE
    CAMEL --> MIDDLEWARE
    REDIS --> TENANT
```

## 🔑 认证与授权

### NextAuth.js 配置

```typescript
// lib/auth.ts
import { NextAuthOptions } from 'next-auth'
import CredentialsProvider from 'next-auth/providers/credentials'
import { PrismaAdapter } from '@next-auth/prisma-adapter'
import { prisma } from './prisma'
import { verifyPassword } from './security'

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }
        
        const user = await prisma.user.findUnique({
          where: { email: credentials.email },
          include: { tenant: true }
        })
        
        if (!user || !user.passwordHash) {
          return null
        }
        
        const isValidPassword = await verifyPassword(
          credentials.password, 
          user.passwordHash
        )
        
        if (!isValidPassword) {
          return null
        }
        
        // 检查用户状态
        if (user.status !== 'ACTIVE') {
          throw new Error('Account is not active')
        }
        
        // 检查租户状态
        if (user.tenant.status !== 'ACTIVE') {
          throw new Error('Tenant account is not active')
        }
        
        return {
          id: user.id,
          email: user.email,
          name: user.name,
          tenantId: user.tenantId,
          role: user.role,
          permissions: user.permissions
        }
      }
    })
  ],
  session: { 
    strategy: 'jwt',
    maxAge: 24 * 60 * 60, // 24小时
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.tenantId = user.tenantId
        token.role = user.role
        token.permissions = user.permissions
      }
      return token
    },
    async session({ session, token }) {
      session.user.id = token.sub!
      session.user.tenantId = token.tenantId as string
      session.user.role = token.role as string
      session.user.permissions = token.permissions as string[]
      return session
    }
  },
  pages: {
    signIn: '/auth/signin',
    error: '/auth/error',
  },
  events: {
    async signIn({ user, account, profile }) {
      // 记录登录事件
      await prisma.auditLog.create({
        data: {
          action: 'USER_SIGNIN',
          userId: user.id,
          tenantId: user.tenantId,
          metadata: {
            provider: account?.provider,
            ip: profile?.ip
          }
        }
      })
    },
    async signOut({ token }) {
      // 记录登出事件
      if (token?.sub && token?.tenantId) {
        await prisma.auditLog.create({
          data: {
            action: 'USER_SIGNOUT',
            userId: token.sub,
            tenantId: token.tenantId as string,
            metadata: {}
          }
        })
      }
    }
  }
}
```

### 角色权限系统

```typescript
// lib/rbac.ts
export enum Role {
  SUPER_ADMIN = 'SUPER_ADMIN',
  TENANT_ADMIN = 'TENANT_ADMIN',
  MANAGER = 'MANAGER',
  USER = 'USER',
  VIEWER = 'VIEWER'
}

export enum Permission {
  // 租户管理
  TENANT_MANAGE = 'tenant:manage',
  TENANT_VIEW = 'tenant:view',
  
  // 用户管理
  USER_CREATE = 'user:create',
  USER_UPDATE = 'user:update',
  USER_DELETE = 'user:delete',
  USER_VIEW = 'user:view',
  
  // 智能体管理
  AGENT_CREATE = 'agent:create',
  AGENT_UPDATE = 'agent:update',
  AGENT_DELETE = 'agent:delete',
  AGENT_VIEW = 'agent:view',
  AGENT_EXECUTE = 'agent:execute',
  
  // 工作流管理
  WORKFLOW_CREATE = 'workflow:create',
  WORKFLOW_UPDATE = 'workflow:update',
  WORKFLOW_DELETE = 'workflow:delete',
  WORKFLOW_VIEW = 'workflow:view',
  WORKFLOW_EXECUTE = 'workflow:execute',
  
  // 知识库管理
  KNOWLEDGE_CREATE = 'knowledge:create',
  KNOWLEDGE_UPDATE = 'knowledge:update',
  KNOWLEDGE_DELETE = 'knowledge:delete',
  KNOWLEDGE_VIEW = 'knowledge:view',
}

// 角色权限映射
export const ROLE_PERMISSIONS: Record<Role, Permission[]> = {
  [Role.SUPER_ADMIN]: Object.values(Permission),
  
  [Role.TENANT_ADMIN]: [
    Permission.TENANT_VIEW,
    Permission.USER_CREATE,
    Permission.USER_UPDATE,
    Permission.USER_DELETE,
    Permission.USER_VIEW,
    Permission.AGENT_CREATE,
    Permission.AGENT_UPDATE,
    Permission.AGENT_DELETE,
    Permission.AGENT_VIEW,
    Permission.AGENT_EXECUTE,
    Permission.WORKFLOW_CREATE,
    Permission.WORKFLOW_UPDATE,
    Permission.WORKFLOW_DELETE,
    Permission.WORKFLOW_VIEW,
    Permission.WORKFLOW_EXECUTE,
    Permission.KNOWLEDGE_CREATE,
    Permission.KNOWLEDGE_UPDATE,
    Permission.KNOWLEDGE_DELETE,
    Permission.KNOWLEDGE_VIEW,
  ],
  
  [Role.MANAGER]: [
    Permission.USER_VIEW,
    Permission.AGENT_CREATE,
    Permission.AGENT_UPDATE,
    Permission.AGENT_VIEW,
    Permission.AGENT_EXECUTE,
    Permission.WORKFLOW_CREATE,
    Permission.WORKFLOW_UPDATE,
    Permission.WORKFLOW_VIEW,
    Permission.WORKFLOW_EXECUTE,
    Permission.KNOWLEDGE_CREATE,
    Permission.KNOWLEDGE_UPDATE,
    Permission.KNOWLEDGE_VIEW,
  ],
  
  [Role.USER]: [
    Permission.AGENT_VIEW,
    Permission.AGENT_EXECUTE,
    Permission.WORKFLOW_VIEW,
    Permission.WORKFLOW_EXECUTE,
    Permission.KNOWLEDGE_VIEW,
  ],
  
  [Role.VIEWER]: [
    Permission.AGENT_VIEW,
    Permission.WORKFLOW_VIEW,
    Permission.KNOWLEDGE_VIEW,
  ]
}

// 权限检查函数
export function hasPermission(userRole: Role, requiredPermission: Permission): boolean {
  const userPermissions = ROLE_PERMISSIONS[userRole] || []
  return userPermissions.includes(requiredPermission)
}

// 权限检查中间件
export function requirePermission(permission: Permission) {
  return (req: any, res: any, next: any) => {
    const userRole = req.session?.user?.role as Role
    
    if (!userRole || !hasPermission(userRole, permission)) {
      return res.status(403).json({ error: 'Insufficient permissions' })
    }
    
    next()
  }
}
```

## 🛡️ 数据库行级安全 (RLS)

### PostgreSQL RLS 策略

```sql
-- 启用 RLS 扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS vector;

-- 创建租户上下文函数
CREATE OR REPLACE FUNCTION current_tenant_id()
RETURNS TEXT AS $$
BEGIN
  RETURN current_setting('app.current_tenant_id', true);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建用户上下文函数
CREATE OR REPLACE FUNCTION current_user_id()
RETURNS TEXT AS $$
BEGIN
  RETURN current_setting('app.current_user_id', true);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 启用所有租户相关表的 RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE agents ENABLE ROW LEVEL SECURITY;
ALTER TABLE workflows ENABLE ROW LEVEL SECURITY;
ALTER TABLE workflow_executions ENABLE ROW LEVEL SECURITY;
ALTER TABLE agent_executions ENABLE ROW LEVEL SECURITY;
ALTER TABLE knowledge_bases ENABLE ROW LEVEL SECURITY;
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_chunks ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- 用户表 RLS 策略
CREATE POLICY tenant_isolation_users ON users
  FOR ALL TO authenticated_user
  USING (tenant_id = current_tenant_id());

-- 智能体表 RLS 策略
CREATE POLICY tenant_isolation_agents ON agents
  FOR ALL TO authenticated_user
  USING (tenant_id = current_tenant_id());

-- 工作流表 RLS 策略
CREATE POLICY tenant_isolation_workflows ON workflows
  FOR ALL TO authenticated_user
  USING (tenant_id = current_tenant_id());

-- 工作流执行表 RLS 策略
CREATE POLICY tenant_isolation_workflow_executions ON workflow_executions
  FOR ALL TO authenticated_user
  USING (tenant_id = current_tenant_id());

-- 智能体执行表 RLS 策略
CREATE POLICY tenant_isolation_agent_executions ON agent_executions
  FOR ALL TO authenticated_user
  USING (tenant_id = current_tenant_id());

-- 知识库表 RLS 策略
CREATE POLICY tenant_isolation_knowledge_bases ON knowledge_bases
  FOR ALL TO authenticated_user
  USING (tenant_id = current_tenant_id());

-- 文档表 RLS 策略
CREATE POLICY tenant_isolation_documents ON documents
  FOR ALL TO authenticated_user
  USING (tenant_id = current_tenant_id());

-- 文档分块表 RLS 策略
CREATE POLICY tenant_isolation_document_chunks ON document_chunks
  FOR ALL TO authenticated_user
  USING (tenant_id = current_tenant_id());

-- 审计日志表 RLS 策略
CREATE POLICY tenant_isolation_audit_logs ON audit_logs
  FOR ALL TO authenticated_user
  USING (tenant_id = current_tenant_id());

-- 创建数据库角色
CREATE ROLE authenticated_user;
GRANT USAGE ON SCHEMA public TO authenticated_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO authenticated_user;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO authenticated_user;
```

### Prisma 中间件强制隔离

```typescript
// lib/prisma-tenant.ts
import { PrismaClient } from '@prisma/client'

export function createTenantPrisma(tenantId: string, userId?: string) {
  const prisma = new PrismaClient()

  // 设置数据库连接上下文
  prisma.$use(async (params, next) => {
    // 设置租户上下文
    await prisma.$executeRaw`SELECT set_config('app.current_tenant_id', ${tenantId}, true)`

    // 设置用户上下文（如果提供）
    if (userId) {
      await prisma.$executeRaw`SELECT set_config('app.current_user_id', ${userId}, true)`
    }

    return next(params)
  })

  // 强制租户隔离中间件
  prisma.$use(async (params, next) => {
    const tenantModels = [
      'user', 'agent', 'workflow', 'workflowExecution', 'agentExecution',
      'knowledgeBase', 'document', 'documentChunk', 'auditLog'
    ]

    const modelName = params.model?.toLowerCase()

    if (modelName && tenantModels.includes(modelName)) {
      // 查询操作：强制添加 tenantId 过滤
      if (['findMany', 'findFirst', 'findUnique', 'count', 'aggregate'].includes(params.action)) {
        if (params.args.where) {
          params.args.where = { ...params.args.where, tenantId }
        } else {
          params.args.where = { tenantId }
        }
      }

      // 创建操作：强制注入 tenantId
      if (params.action === 'create') {
        if (params.args.data) {
          params.args.data = { ...params.args.data, tenantId }
        }
      }

      // 批量创建：强制注入 tenantId
      if (params.action === 'createMany') {
        if (Array.isArray(params.args.data)) {
          params.args.data = params.args.data.map((item: any) => ({
            ...item,
            tenantId
          }))
        }
      }

      // 更新操作：确保只能更新本租户数据
      if (['update', 'updateMany', 'delete', 'deleteMany', 'upsert'].includes(params.action)) {
        if (params.args.where) {
          params.args.where = { ...params.args.where, tenantId }
        } else {
          params.args.where = { tenantId }
        }
      }
    }

    return next(params)
  })

  return prisma
}

// tRPC 上下文中使用
export async function createTRPCContext({ req, res }: CreateNextContextOptions) {
  const session = await getServerAuthSession({ req, res })

  if (!session?.user?.tenantId) {
    // 对于未认证的请求，返回受限的上下文
    return {
      session: null,
      db: null,
      tenantId: null,
      userId: null
    }
  }

  const db = createTenantPrisma(session.user.tenantId, session.user.id)

  return {
    session,
    db,
    tenantId: session.user.tenantId,
    userId: session.user.id
  }
}
```

## 🔒 API 安全控制

### tRPC 权限中间件

```typescript
// server/api/trpc.ts
import { TRPCError, initTRPC } from '@trpc/server'
import { type CreateNextContextOptions } from '@trpc/server/adapters/next'
import { ZodError } from 'zod'
import { hasPermission, Permission, Role } from '@/lib/rbac'

const t = initTRPC.context<typeof createTRPCContext>().create({
  transformer: superjson,
  errorFormatter({ shape, error }) {
    return {
      ...shape,
      data: {
        ...shape.data,
        zodError:
          error.cause instanceof ZodError ? error.cause.flatten() : null,
      },
    }
  },
})

export const createTRPCRouter = t.router

// 公开过程（无需认证）
export const publicProcedure = t.procedure

// 受保护过程（需要认证）
export const protectedProcedure = t.procedure.use(({ ctx, next }) => {
  if (!ctx.session || !ctx.session.user) {
    throw new TRPCError({ code: 'UNAUTHORIZED' })
  }

  if (!ctx.tenantId) {
    throw new TRPCError({
      code: 'FORBIDDEN',
      message: 'No tenant context'
    })
  }

  return next({
    ctx: {
      ...ctx,
      session: { ...ctx.session, user: ctx.session.user },
      db: ctx.db!,
      tenantId: ctx.tenantId,
      userId: ctx.userId!
    },
  })
})

// 权限检查过程
export function requirePermission(permission: Permission) {
  return protectedProcedure.use(({ ctx, next }) => {
    const userRole = ctx.session.user.role as Role

    if (!hasPermission(userRole, permission)) {
      throw new TRPCError({
        code: 'FORBIDDEN',
        message: `Missing permission: ${permission}`
      })
    }

    return next({ ctx })
  })
}

// 管理员权限过程
export const adminProcedure = requirePermission(Permission.TENANT_MANAGE)

// 用户管理权限过程
export const userManagementProcedure = requirePermission(Permission.USER_CREATE)
```

### API 速率限制

```typescript
// lib/rate-limit.ts
import { Redis } from 'ioredis'
import { TRPCError } from '@trpc/server'

const redis = new Redis(process.env.REDIS_URL!)

interface RateLimitConfig {
  windowMs: number // 时间窗口（毫秒）
  maxRequests: number // 最大请求数
  keyGenerator?: (ctx: any) => string // 自定义键生成器
}

export function createRateLimit(config: RateLimitConfig) {
  return async (ctx: any) => {
    const key = config.keyGenerator
      ? config.keyGenerator(ctx)
      : `rate_limit:${ctx.tenantId}:${ctx.userId}`

    const current = await redis.incr(key)

    if (current === 1) {
      // 设置过期时间
      await redis.pexpire(key, config.windowMs)
    }

    if (current > config.maxRequests) {
      const ttl = await redis.pttl(key)
      throw new TRPCError({
        code: 'TOO_MANY_REQUESTS',
        message: `Rate limit exceeded. Try again in ${Math.ceil(ttl / 1000)} seconds.`
      })
    }

    return {
      remaining: Math.max(0, config.maxRequests - current),
      resetTime: Date.now() + (await redis.pttl(key))
    }
  }
}

// 使用示例
export const rateLimitedProcedure = protectedProcedure.use(async ({ ctx, next }) => {
  await createRateLimit({
    windowMs: 60 * 1000, // 1分钟
    maxRequests: 100, // 每分钟100次请求
  })(ctx)

  return next({ ctx })
})

// 智能体执行限制（更严格）
export const agentExecutionLimit = createRateLimit({
  windowMs: 60 * 1000, // 1分钟
  maxRequests: 10, // 每分钟10次智能体执行
  keyGenerator: (ctx) => `agent_exec:${ctx.tenantId}:${ctx.userId}`
})
```

## 📊 审计日志系统

### 审计日志模型

```typescript
// lib/audit.ts
export enum AuditAction {
  // 用户操作
  USER_SIGNIN = 'USER_SIGNIN',
  USER_SIGNOUT = 'USER_SIGNOUT',
  USER_CREATE = 'USER_CREATE',
  USER_UPDATE = 'USER_UPDATE',
  USER_DELETE = 'USER_DELETE',

  // 智能体操作
  AGENT_CREATE = 'AGENT_CREATE',
  AGENT_UPDATE = 'AGENT_UPDATE',
  AGENT_DELETE = 'AGENT_DELETE',
  AGENT_EXECUTE = 'AGENT_EXECUTE',

  // 工作流操作
  WORKFLOW_CREATE = 'WORKFLOW_CREATE',
  WORKFLOW_UPDATE = 'WORKFLOW_UPDATE',
  WORKFLOW_DELETE = 'WORKFLOW_DELETE',
  WORKFLOW_EXECUTE = 'WORKFLOW_EXECUTE',

  // 知识库操作
  KNOWLEDGE_CREATE = 'KNOWLEDGE_CREATE',
  KNOWLEDGE_UPDATE = 'KNOWLEDGE_UPDATE',
  KNOWLEDGE_DELETE = 'KNOWLEDGE_DELETE',

  // 系统操作
  SYSTEM_CONFIG_UPDATE = 'SYSTEM_CONFIG_UPDATE',
  SECURITY_VIOLATION = 'SECURITY_VIOLATION',
}

export interface AuditLogData {
  action: AuditAction
  tenantId: string
  userId?: string
  resourceType?: string
  resourceId?: string
  metadata?: Record<string, any>
  ipAddress?: string
  userAgent?: string
}

export class AuditLogger {
  private prisma: PrismaClient

  constructor(prisma: PrismaClient) {
    this.prisma = prisma
  }

  async log(data: AuditLogData): Promise<void> {
    try {
      await this.prisma.auditLog.create({
        data: {
          action: data.action,
          tenantId: data.tenantId,
          userId: data.userId,
          resourceType: data.resourceType,
          resourceId: data.resourceId,
          metadata: data.metadata || {},
          ipAddress: data.ipAddress,
          userAgent: data.userAgent,
          timestamp: new Date()
        }
      })
    } catch (error) {
      console.error('Failed to write audit log:', error)
      // 审计日志失败不应该影响业务操作
    }
  }

  async logSecurityViolation(
    tenantId: string,
    userId: string | undefined,
    violation: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.log({
      action: AuditAction.SECURITY_VIOLATION,
      tenantId,
      userId,
      metadata: {
        violation,
        ...metadata
      }
    })
  }
}

// tRPC 审计中间件
export const auditMiddleware = t.middleware(async ({ ctx, path, type, next }) => {
  const start = Date.now()
  const result = await next()
  const duration = Date.now() - start

  // 只审计变更操作
  if (type === 'mutation' && ctx.session?.user) {
    const auditor = new AuditLogger(ctx.db)

    await auditor.log({
      action: mapPathToAction(path),
      tenantId: ctx.tenantId!,
      userId: ctx.userId,
      metadata: {
        path,
        duration,
        success: result.ok
      }
    })
  }

  return result
})

function mapPathToAction(path: string): AuditAction {
  // 根据 tRPC 路径映射到审计动作
  if (path.includes('agent.create')) return AuditAction.AGENT_CREATE
  if (path.includes('agent.update')) return AuditAction.AGENT_UPDATE
  if (path.includes('agent.delete')) return AuditAction.AGENT_DELETE
  if (path.includes('agent.execute')) return AuditAction.AGENT_EXECUTE

  if (path.includes('workflow.create')) return AuditAction.WORKFLOW_CREATE
  if (path.includes('workflow.update')) return AuditAction.WORKFLOW_UPDATE
  if (path.includes('workflow.delete')) return AuditAction.WORKFLOW_DELETE
  if (path.includes('workflow.execute')) return AuditAction.WORKFLOW_EXECUTE

  // 默认返回系统配置更新
  return AuditAction.SYSTEM_CONFIG_UPDATE
}
```

## 🛡️ 数据加密与安全

### 敏感数据加密

```typescript
// lib/encryption.ts
import crypto from 'crypto'
import bcrypt from 'bcryptjs'

export class EncryptionService {
  private readonly algorithm = 'aes-256-gcm'
  private readonly key: Buffer

  constructor() {
    const keyString = process.env.ENCRYPTION_KEY
    if (!keyString) {
      throw new Error('ENCRYPTION_KEY environment variable is required')
    }
    this.key = Buffer.from(keyString, 'hex')
  }

  // 密码哈希
  async hashPassword(password: string): Promise<string> {
    const saltRounds = 12
    return bcrypt.hash(password, saltRounds)
  }

  async verifyPassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash)
  }

  // 敏感数据加密
  encrypt(text: string): string {
    const iv = crypto.randomBytes(16)
    const cipher = crypto.createCipherGCM(this.algorithm, this.key, iv)

    let encrypted = cipher.update(text, 'utf8', 'hex')
    encrypted += cipher.final('hex')

    const authTag = cipher.getAuthTag()

    // 格式: iv:authTag:encryptedData
    return `${iv.toString('hex')}:${authTag.toString('hex')}:${encrypted}`
  }

  decrypt(encryptedData: string): string {
    const [ivHex, authTagHex, encrypted] = encryptedData.split(':')

    if (!ivHex || !authTagHex || !encrypted) {
      throw new Error('Invalid encrypted data format')
    }

    const iv = Buffer.from(ivHex, 'hex')
    const authTag = Buffer.from(authTagHex, 'hex')

    const decipher = crypto.createDecipherGCM(this.algorithm, this.key, iv)
    decipher.setAuthTag(authTag)

    let decrypted = decipher.update(encrypted, 'hex', 'utf8')
    decrypted += decipher.final('utf8')

    return decrypted
  }

  // 生成安全令牌
  generateSecureToken(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex')
  }

  // API密钥哈希
  hashApiKey(apiKey: string): string {
    return crypto.createHash('sha256').update(apiKey).digest('hex')
  }
}

// 单例实例
export const encryptionService = new EncryptionService()
```

### 输入验证与清理

```typescript
// lib/validation.ts
import { z } from 'zod'
import DOMPurify from 'isomorphic-dompurify'

// 通用验证规则
export const commonSchemas = {
  tenantId: z.string().cuid(),
  userId: z.string().cuid(),
  email: z.string().email().max(255),
  password: z.string().min(8).max(128).regex(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    'Password must contain at least one lowercase letter, one uppercase letter, one number, and one special character'
  ),
  name: z.string().min(1).max(100).trim(),
  description: z.string().max(1000).optional(),
}

// HTML内容清理
export function sanitizeHtml(html: string): string {
  return DOMPurify.sanitize(html, {
    ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'u', 'ol', 'ul', 'li'],
    ALLOWED_ATTR: []
  })
}

// SQL注入防护（虽然Prisma已经防护，但额外检查）
export function validateSqlInput(input: string): boolean {
  const sqlInjectionPattern = /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)|(-{2})|(\*\/)|(\*)|(\bOR\b.*=.*)|(\bAND\b.*=.*)/i
  return !sqlInjectionPattern.test(input)
}

// XSS防护
export function sanitizeUserInput(input: string): string {
  return input
    .replace(/[<>]/g, '') // 移除尖括号
    .replace(/javascript:/gi, '') // 移除javascript协议
    .replace(/on\w+=/gi, '') // 移除事件处理器
    .trim()
}

// 文件上传验证
export const fileUploadSchema = z.object({
  filename: z.string().min(1).max(255).refine(
    (filename) => {
      const allowedExtensions = ['.pdf', '.doc', '.docx', '.txt', '.md']
      return allowedExtensions.some(ext => filename.toLowerCase().endsWith(ext))
    },
    'File type not allowed'
  ),
  size: z.number().max(10 * 1024 * 1024), // 10MB限制
  mimeType: z.enum([
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain',
    'text/markdown'
  ])
})
```

---

**文档版本**: 1.0.0
**最后更新**: 2024-12-19
**多租户安全版本**: v1.0
