# SGASpace 企业智能工作台开发计划

## 📋 项目概述

基于CAMEL框架的现代化企业智能工作台，采用**混合架构**设计：Next.js 15 Web应用 + 独立CAMEL智能体服务，通过Redis实现高性能的任务调度和实时通信。

### 🎯 核心目标

- **智能体协作平台** - 多个AI智能体协同工作
- **可视化工作流设计** - 拖拽式工作流编辑器
- **企业级多租户** - 完整的租户隔离和权限管理
- **知识库智能化** - pgvector向量检索，支持语义搜索
- **实时状态同步** - Redis Pub/Sub实时通信

### 🏗️ 更新后技术架构

```mermaid
graph TB
    subgraph "Web应用层"
        A[Next.js 15 App Router]
        B[React Server Components]
        C[shadcn/ui + Tailwind CSS]
        D[React Flow - 工作流设计器]
    end

    subgraph "API层"
        E[tRPC - 类型安全API]
        F[NextAuth.js - 认证]
        G[Redis Pub/Sub - 实时通信]
    end

    subgraph "CAMEL智能体服务 (Python)"
        H[FastAPI 网关]
        I[CAMEL Workforce]
        J[CAMEL RolePlaying]
        K[TaskPlannerAgent]
        L[ChatAgent + Toolkits]
    end

    subgraph "数据层"
        M[Prisma ORM]
        N[PostgreSQL + pgvector]
        O[Redis - 缓存/队列/实时]
        P[MinIO/S3 - 对象存储]
    end

    A --> E
    E --> H
    H --> I
    E --> M
    M --> N
    E --> O
    G --> O
```

## 📅 开发阶段规划（收敛P0范围）

### Phase 0: MVP核心功能 (4周) - **收敛范围**

**目标**: 交付可用的端到端工作流，验证核心价值

#### P0 核心任务:
1. **项目基础架构**
   - 创建Next.js 15项目结构
   - 配置TypeScript、Tailwind CSS、shadcn/ui
   - 设置ESLint、Prettier代码规范

2. **数据库设计（简化）**
   - 设计Prisma数据库模型（核心表）
   - 配置PostgreSQL + pgvector
   - 启用RLS多租户安全策略

3. **用户认证系统（基础）**
   - 集成NextAuth.js
   - 实现邮箱密码登录
   - JWT令牌管理

4. **多租户安全（强制）**
   - Postgres RLS策略
   - Prisma中间件强制租户隔离
   - 基础RBAC权限

5. **CAMEL独立服务**
   - 创建FastAPI + CAMEL服务
   - 定义最小API接口（/agents/execute, /workflows/step）
   - HTTP调用集成（带重试机制）

6. **Redis队列与实时**
   - BullMQ工作流任务队列
   - Redis Pub/Sub实时状态推送
   - 基础状态管理

7. **tRPC API架构（最小）**
   - 智能体CRUD + 执行
   - 工作流CRUD + 执行
   - 错误处理与权限检查

8. **基础UI（核心功能）**
   - 智能体管理页面
   - 简单工作流设计器（3-5个节点类型）
   - 执行状态监控

9. **Docker容器化**
   - Web应用 + CAMEL服务 + PostgreSQL + Redis
   - docker-compose一键启动
   - 健康检查配置

### Phase 1: 功能完善与优化 (6-10周)

**目标**: 在MVP基础上完善功能，提升用户体验

#### P1 任务:
1. **智能体市场系统** ⭐ **新增核心功能**
   - 智能体市场前端界面
   - 外部智能体聘用流程
   - 智能体评价与评论系统
   - 基础支付集成（Stripe）

2. **知识库系统**
   - 文档上传与向量化处理
   - pgvector语义检索优化
   - 支持多种文档格式（PDF、Word、Markdown）

3. **工作流高级功能**
   - 条件分支、循环、并行节点
   - 工作流版本管理与回滚
   - 子工作流与模板系统

4. **团队协作功能**
   - 多用户并发编辑工作流
   - 冲突解决与合并机制
   - 评论与活动日志

5. **CAMEL智能体扩展**
   - 更多智能体类型（决策支持、工具集成）
   - RolePlaying角色扮演模式
   - Workforce团队协作模式

6. **企业级安全**
   - OAuth集成（Google、Microsoft）
   - 审计日志与合规报告
   - API密钥管理

7. **性能优化**
   - 数据库查询优化
   - Redis缓存策略
   - 前端代码分割与懒加载

8. **监控与可观测性**
   - OpenTelemetry集成
   - Prometheus指标收集
   - Grafana仪表板

### Phase 2: 企业级功能与生产准备 (后续规划)

**目标**: 完善企业级功能，准备规模化部署

#### P2+ 任务:
1. **智能体市场生态完善** ⭐ **市场生态扩展**
   - 智能体质量认证体系
   - 第三方开发者平台
   - 智能体SDK和开发工具
   - 收益分成与结算系统
   - 智能推荐与个性化

2. **高级智能体协作**
   - Society智能体社会模式
   - 复杂任务自动分解与分配
   - 智能体间通信协议优化

3. **企业集成**
   - SAML/SCIM单点登录
   - 企业目录服务集成
   - API网关与第三方集成

4. **计费与配额系统**
   - 基于使用量的计费模型
   - 租户资源配额管理
   - 成本分析与报告

5. **高级安全与合规**
   - 数据驻留与地域合规
   - GDPR/CCPA数据保护
   - 安全扫描与漏洞管理

6. **多区域部署**
   - 全球CDN与边缘计算
   - 数据同步与一致性
   - 灾难恢复与备份

7. **AI模型管理**
   - 多模型支持与切换
   - 模型微调与优化
   - 成本优化与模型选择

8. **高级分析与洞察**
   - 用户行为分析
   - 工作流效率分析
   - 智能体性能优化建议

## 📊 更新后的项目里程碑

| 里程碑 | 时间节点 | 主要交付物 | 成功标准 |
|--------|----------|------------|----------|
| **P0 MVP** | 第4周 | 端到端工作流可用 | 用户可以创建智能体并执行简单工作流 |
| **P1 完善版** | 第10-14周 | 功能完整的系统 | 支持知识库、团队协作、高级工作流 |
| **P2 企业版** | 后续规划 | 企业级完整产品 | 通过安全审计、支持大规模部署 |

## 🎯 P0 MVP 验收标准

### 核心功能验收
- ✅ 用户可以注册/登录（邮箱密码）
- ✅ 创建和管理智能体（至少2种类型）
- ✅ 设计简单工作流（3-5个节点）
- ✅ 执行工作流并查看结果
- ✅ 实时查看执行状态
- ✅ 多租户数据完全隔离

### 技术指标（P0）
- **部署时间**: < 5分钟（docker-compose up）
- **页面加载**: < 3秒（开发环境可接受）
- **智能体响应**: < 30秒（CAMEL服务调用）
- **并发用户**: 100（初期目标）
- **系统稳定性**: 基本可用，有监控

### 业务指标（P0）
- **用户上手时间**: < 15分钟（创建第一个工作流）
- **工作流创建**: < 10分钟（简单流程）
- **错误恢复**: 有基础错误提示和重试
- **数据安全**: RLS策略生效，无跨租户数据泄露

## 🔧 技术债务管理

### P0 阶段允许的技术债务
- UI/UX可以简陋，但功能完整
- 错误处理可以基础，但不能崩溃
- 性能可以一般，但要稳定可观测
- 测试覆盖率可以较低，但核心路径要有测试

### P1 阶段必须偿还的技术债务
- 完善UI/UX设计和交互体验
- 增强错误处理和用户提示
- 性能优化和缓存策略
- 提高测试覆盖率到80%+

## 🚀 快速启动指南

### 开发环境搭建
```bash
# 1. 克隆项目
git clone <repository-url>
cd sgaspace

# 2. 环境配置
cp .env.example .env
# 编辑 .env 文件，配置必要的环境变量

# 3. 启动服务
docker-compose up -d

# 4. 数据库初始化
npm run db:migrate
npm run db:seed

# 5. 访问应用
open http://localhost:3000
```

### P0 开发检查清单
- [ ] 基础项目结构搭建完成
- [ ] 数据库模型和RLS策略配置
- [ ] NextAuth认证系统集成
- [ ] CAMEL独立服务创建
- [ ] Redis队列和实时通信配置
- [ ] tRPC API基础路由实现
- [ ] 智能体管理界面开发
- [ ] **智能体市场基础界面** ⭐ 新增
- [ ] **外部智能体聘用流程** ⭐ 新增
- [ ] 工作流设计器基础版本
- [ ] 执行引擎和状态监控
- [ ] Docker容器化配置完成

---

**文档版本**: 2.0.0
**最后更新**: 2024-12-19
**开发计划版本**: v2.0 (收敛P0范围)
