# SGASpace CAMEL 框架集成设计

## 🐪 CAMEL 框架概述

CAMEL (Communicative Agents for "Mind" Exploration of Large Scale Language Model Society) 是一个强大的多智能体协作框架。我们将CAMEL作为SGASpace的**独立智能体服务**，通过FastAPI提供专业的多智能体协作能力。

### CAMEL 核心优势

1. **角色扮演机制** - 智能体具有明确的角色定义和专业能力
2. **多智能体协作** - 支持复杂的智能体间协作模式
3. **任务分解能力** - 自动将复杂任务分解为子任务
4. **工具集成** - 丰富的工具包，支持代码执行、搜索等
5. **记忆管理** - 智能体具有上下文记忆能力
6. **可观测性** - 完整的执行日志和状态监控

## 🏗️ CAMEL 独立服务架构

### 整体集成架构

```mermaid
graph TB
    subgraph "SGASpace Web 应用"
        A[Next.js 工作流设计器]
        B[智能体管理界面]
        C[实时监控面板]
    end

    subgraph "SGASpace API 层"
        D[tRPC API Routes]
        E[多租户中间件]
        F[认证授权]
    end

    subgraph "SGASpace 业务层"
        G[工作流引擎]
        H[智能体管理服务]
        I[Redis 任务队列]
    end

    subgraph "CAMEL 独立服务 (Python)"
        J[FastAPI 网关]
        K[CAMEL Workforce]
        L[CAMEL RolePlaying]
        M[CAMEL ChatAgent]
        N[CAMEL TaskAgent]
        O[工具集成模块]
    end

    subgraph "数据存储层"
        P[PostgreSQL + pgvector]
        Q[Redis 缓存/队列/实时]
        R[MinIO/S3 对象存储]
    end

    A --> D
    B --> D
    C --> D
    D --> E
    E --> F
    F --> G
    G --> H
    H --> I

    G --> J
    H --> J
    I --> J

    J --> K
    J --> L
    J --> M
    J --> N
    J --> O

    G --> P
    H --> P
    I --> Q
    J --> P
```

### CAMEL 智能体类型映射

| SGASpace 智能体类型 | CAMEL 对应实现 | 主要能力 |
|-------------------|---------------|---------|
| **任务规划智能体** | TaskPlannerAgent + Workforce | 任务分解、计划制定、资源分配 |
| **知识管理智能体** | ChatAgent + SearchToolkit | 知识检索、内容生成、信息整理 |
| **决策支持智能体** | ChatAgent + ThinkingToolkit | 数据分析、风险评估、决策建议 |
| **工具集成智能体** | ToolkitAgent + 自定义工具 | API调用、系统集成、自动化操作 |
| **协作协调智能体** | RolePlaying + CriticAgent | 智能体协调、质量控制、流程优化 |

## 🔧 CAMEL 独立服务实现

### 1. FastAPI CAMEL 服务网关

```python
# camel_service/main.py
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any, Optional, List
import asyncio
import uuid
from datetime import datetime

from camel.agents import ChatAgent, TaskPlannerAgent
from camel.societies import RolePlaying
from camel.societies.workforce import Workforce
from camel.models import ModelFactory
from camel.types import ModelPlatformType, ModelType, TaskType
from camel.toolkits import (
    SearchToolkit,
    CodeExecutionToolkit,
    TaskPlanningToolkit,
    ThinkingToolkit
)

app = FastAPI(
    title="SGASpace CAMEL Service",
    description="独立的CAMEL智能体服务",
    version="1.0.0"
)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 请求/响应模型
class AgentExecuteRequest(BaseModel):
    agent_id: str
    agent_type: str
    input: Dict[str, Any]
    context: Dict[str, Any] = {}
    tenant_id: str
    user_id: str

class AgentExecuteResponse(BaseModel):
    success: bool
    output: Dict[str, Any] = {}
    logs: List[str] = []
    tokens_used: int = 0
    execution_time: float = 0.0
    error: Optional[str] = None

class WorkflowStepRequest(BaseModel):
    workflow_id: str
    step_id: str
    agent_configs: List[Dict[str, Any]]
    input: Dict[str, Any]
    tenant_id: str

class WorkflowStepResponse(BaseModel):
    success: bool
    results: Dict[str, Any] = {}
    step_logs: List[Dict[str, Any]] = []
    error: Optional[str] = None

# 全局服务实例
class CAMELServiceManager:
    def __init__(self):
        self.agents: Dict[str, ChatAgent] = {}
        self.workforces: Dict[str, Workforce] = {}
        self.execution_logs: Dict[str, List] = {}

    async def create_agent(self, agent_config: Dict[str, Any]) -> ChatAgent:
        """创建CAMEL智能体实例"""
        agent_type = agent_config.get('type', 'CHAT')

        if agent_type == 'TASK_PLANNING':
            agent = TaskPlannerAgent(
                model=ModelFactory.create(
                    model_platform=ModelPlatformType.OPENAI,
                    model_type=ModelType.GPT_4O_MINI
                )
            )
        else:
            # 生成系统消息
            system_message = self.generate_system_message(agent_config)

            agent = ChatAgent(
                system_message=system_message,
                model=ModelFactory.create(
                    model_platform=ModelPlatformType.OPENAI,
                    model_type=ModelType.GPT_4O_MINI
                )
            )

            # 添加工具包
            tools = self.get_agent_tools(agent_type)
            for tool in tools:
                agent.add_toolkit(tool)

        return agent

    def generate_system_message(self, agent_config: Dict[str, Any]) -> str:
        """生成智能体系统消息"""
        agent_type = agent_config.get('type', 'CHAT')
        agent_name = agent_config.get('name', '智能助手')
        agent_desc = agent_config.get('description', '')

        role_templates = {
            'TASK_PLANNING': f"""
你是一个专业的任务规划专家 - {agent_name}。

{agent_desc}

你的主要职责：
1. 分析复杂任务并分解为可执行的子任务
2. 制定详细的执行计划和时间安排
3. 识别任务依赖关系和资源需求
4. 监控任务执行进度并调整计划

请始终提供结构化、可执行的任务规划方案。
            """,
            'KNOWLEDGE_MANAGEMENT': f"""
你是一个专业的知识管理专家 - {agent_name}。

{agent_desc}

你的主要职责：
1. 检索和整理相关知识信息
2. 回答基于知识库的问题
3. 总结和分析文档内容
4. 提供准确的信息支持

请始终基于可靠的信息源提供答案，并注明信息来源。
            """,
            'DECISION_SUPPORT': f"""
你是一个专业的决策支持分析师 - {agent_name}。

{agent_desc}

你的主要职责：
1. 分析数据和信息，识别关键模式
2. 评估风险和机会
3. 提供客观的决策建议
4. 生成详细的分析报告

请确保你的分析客观、全面、有据可依。
            """
        }

        return role_templates.get(agent_type, f"""
你是一个专业的智能助手 - {agent_name}。

{agent_desc}

请根据用户的需求提供专业、准确、有帮助的回答。
        """)

    def get_agent_tools(self, agent_type: str) -> List:
        """获取智能体工具包"""
        tool_mapping = {
            'TASK_PLANNING': [TaskPlanningToolkit(), ThinkingToolkit()],
            'KNOWLEDGE_MANAGEMENT': [SearchToolkit()],
            'DECISION_SUPPORT': [ThinkingToolkit()],
            'TOOL_INTEGRATION': [CodeExecutionToolkit()]
        }

        return tool_mapping.get(agent_type, [])

# 全局服务管理器
service_manager = CAMELServiceManager()
```

### 2. FastAPI 路由端点

```python
# camel_service/main.py (续)

@app.post("/agents/execute", response_model=AgentExecuteResponse)
async def execute_agent(request: AgentExecuteRequest):
    """执行智能体任务"""
    start_time = datetime.now()
    execution_id = str(uuid.uuid4())

    try:
        # 创建智能体实例
        agent_config = {
            'type': request.agent_type,
            'name': f"Agent-{request.agent_id}",
            'description': request.context.get('description', '')
        }

        agent = await service_manager.create_agent(agent_config)

        # 执行智能体任务
        user_message = request.input.get('message', '')
        if not user_message:
            raise ValueError("Missing 'message' in input")

        response = agent.step(user_message)

        # 计算执行时间
        execution_time = (datetime.now() - start_time).total_seconds()

        # 记录执行日志
        log_entry = {
            'execution_id': execution_id,
            'agent_id': request.agent_id,
            'tenant_id': request.tenant_id,
            'input': request.input,
            'output': response.msg.content,
            'execution_time': execution_time,
            'timestamp': datetime.now().isoformat()
        }

        if execution_id not in service_manager.execution_logs:
            service_manager.execution_logs[execution_id] = []
        service_manager.execution_logs[execution_id].append(log_entry)

        return AgentExecuteResponse(
            success=True,
            output={
                'content': response.msg.content,
                'role': response.msg.role_name,
                'metadata': response.info
            },
            logs=[f"Agent executed successfully in {execution_time:.2f}s"],
            tokens_used=response.info.get('token_usage', 0),
            execution_time=execution_time
        )

    except Exception as e:
        execution_time = (datetime.now() - start_time).total_seconds()

        return AgentExecuteResponse(
            success=False,
            error=str(e),
            execution_time=execution_time,
            logs=[f"Agent execution failed: {str(e)}"]
        )

@app.post("/workflows/step", response_model=WorkflowStepResponse)
async def execute_workflow_step(request: WorkflowStepRequest):
    """执行工作流步骤（多智能体协作）"""
    start_time = datetime.now()

    try:
        # 创建Workforce实例
        workforce_config = {
            'id': f"workflow-{request.workflow_id}-{request.step_id}",
            'workers': []
        }

        # 为每个智能体配置创建工作者
        for agent_config in request.agent_configs:
            worker = {
                'role': agent_config['name'],
                'system_message': service_manager.generate_system_message(agent_config),
                'model_platform': 'OPENAI',
                'model_type': 'GPT_4O_MINI'
            }
            workforce_config['workers'].append(worker)

        # 创建并执行Workforce
        workforce = Workforce(workforce_config['id'])

        # 添加工作者到Workforce
        for worker_config in workforce_config['workers']:
            agent = ChatAgent(
                system_message=worker_config['system_message'],
                model=ModelFactory.create(
                    model_platform=ModelPlatformType.OPENAI,
                    model_type=ModelType.GPT_4O_MINI
                )
            )

            workforce.add_single_agent_worker(
                worker_config['role'],
                worker=agent
            )

        # 执行任务
        task = request.input.get('task', '')
        result = await workforce.process_task(task)

        execution_time = (datetime.now() - start_time).total_seconds()

        return WorkflowStepResponse(
            success=True,
            results={
                'output': result,
                'task_history': workforce.get_task_history() if hasattr(workforce, 'get_task_history') else []
            },
            step_logs=[{
                'step_id': request.step_id,
                'execution_time': execution_time,
                'status': 'completed'
            }]
        )

    except Exception as e:
        execution_time = (datetime.now() - start_time).total_seconds()

        return WorkflowStepResponse(
            success=False,
            error=str(e),
            step_logs=[{
                'step_id': request.step_id,
                'execution_time': execution_time,
                'status': 'failed',
                'error': str(e)
            }]
        )

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": "SGASpace CAMEL Service",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/agents/{agent_id}/logs")
async def get_agent_logs(agent_id: str):
    """获取智能体执行日志"""
    logs = []
    for execution_id, execution_logs in service_manager.execution_logs.items():
        for log in execution_logs:
            if log['agent_id'] == agent_id:
                logs.append(log)

    return {"logs": logs}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

### 3. Next.js 中的 CAMEL 服务客户端

```typescript
// lib/camel-client.ts
export class CAMELServiceClient {
  private baseUrl: string

  constructor() {
    this.baseUrl = process.env.CAMEL_SERVICE_URL || 'http://localhost:8000'
  }

  async executeAgent(request: {
    agentId: string
    agentType: string
    input: any
    context?: any
    tenantId: string
    userId: string
  }): Promise<{
    success: boolean
    output?: any
    logs?: string[]
    tokensUsed?: number
    executionTime?: number
    error?: string
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/agents/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          agent_id: request.agentId,
          agent_type: request.agentType,
          input: request.input,
          context: request.context || {},
          tenant_id: request.tenantId,
          user_id: request.userId
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()

      return {
        success: result.success,
        output: result.output,
        logs: result.logs,
        tokensUsed: result.tokens_used,
        executionTime: result.execution_time,
        error: result.error
      }
    } catch (error) {
      console.error('CAMEL service call failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  async executeWorkflowStep(request: {
    workflowId: string
    stepId: string
    agentConfigs: any[]
    input: any
    tenantId: string
  }): Promise<{
    success: boolean
    results?: any
    stepLogs?: any[]
    error?: string
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/workflows/step`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          workflow_id: request.workflowId,
          step_id: request.stepId,
          agent_configs: request.agentConfigs,
          input: request.input,
          tenant_id: request.tenantId
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()

      return {
        success: result.success,
        results: result.results,
        stepLogs: result.step_logs,
        error: result.error
      }
    } catch (error) {
      console.error('CAMEL workflow step failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/health`)
      return response.ok
    } catch (error) {
      console.error('CAMEL service health check failed:', error)
      return false
    }
  }

  async getAgentLogs(agentId: string): Promise<any[]> {
    try {
      const response = await fetch(`${this.baseUrl}/agents/${agentId}/logs`)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      return result.logs || []
    } catch (error) {
      console.error('Failed to get agent logs:', error)
      return []
    }
  }
}

// 单例实例
export const camelClient = new CAMELServiceClient()
```

### 4. tRPC 路由集成

```typescript
// server/api/routers/agent.ts
import { z } from 'zod'
import { createTRPCRouter, protectedProcedure } from '@/server/api/trpc'
import { camelClient } from '@/lib/camel-client'

const executeAgentSchema = z.object({
  agentId: z.string(),
  input: z.object({
    message: z.string()
  }),
  context: z.record(z.any()).optional()
})

export const agentRouter = createTRPCRouter({
  execute: protectedProcedure
    .input(executeAgentSchema)
    .mutation(async ({ input, ctx }) => {
      // 获取智能体配置
      const agent = await ctx.db.agent.findUnique({
        where: {
          id: input.agentId,
          tenantId: ctx.session.user.tenantId
        }
      })

      if (!agent) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Agent not found'
        })
      }

      // 调用CAMEL服务
      const result = await camelClient.executeAgent({
        agentId: input.agentId,
        agentType: agent.type,
        input: input.input,
        context: input.context,
        tenantId: ctx.session.user.tenantId,
        userId: ctx.session.user.id
      })

      if (!result.success) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: result.error || 'Agent execution failed'
        })
      }

      // 记录执行历史
      await ctx.db.agentExecution.create({
        data: {
          agentId: input.agentId,
          input: input.input,
          output: result.output,
          status: 'COMPLETED',
          executionTime: result.executionTime || 0,
          tokensUsed: result.tokensUsed || 0,
          tenantId: ctx.session.user.tenantId,
          userId: ctx.session.user.id
        }
      })

      return {
        success: true,
        output: result.output,
        logs: result.logs,
        executionTime: result.executionTime,
        tokensUsed: result.tokensUsed
      }
    }),

  list: protectedProcedure
    .input(z.object({
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(100).default(20),
      type: z.enum(['TASK_PLANNING', 'KNOWLEDGE_MANAGEMENT', 'DECISION_SUPPORT', 'TOOL_INTEGRATION']).optional(),
    }))
    .query(async ({ input, ctx }) => {
      const { page, limit, type } = input
      const skip = (page - 1) * limit

      const where = {
        tenantId: ctx.session.user.tenantId,
        ...(type && { type }),
      }

      const [agents, total] = await Promise.all([
        ctx.db.agent.findMany({
          where,
          skip,
          take: limit,
          include: {
            _count: {
              select: { executions: true }
            }
          },
          orderBy: { createdAt: 'desc' }
        }),
        ctx.db.agent.count({ where })
      ])

      return {
        agents,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    }),

  create: protectedProcedure
    .input(z.object({
      name: z.string().min(1),
      description: z.string().optional(),
      type: z.enum(['TASK_PLANNING', 'KNOWLEDGE_MANAGEMENT', 'DECISION_SUPPORT', 'TOOL_INTEGRATION']),
      config: z.record(z.any()).default({})
    }))
    .mutation(async ({ input, ctx }) => {
      const agent = await ctx.db.agent.create({
        data: {
          ...input,
          tenantId: ctx.session.user.tenantId,
          createdById: ctx.session.user.id,
          status: 'ACTIVE'
        }
      })

      return agent
    }),

  getExecutionHistory: protectedProcedure
    .input(z.object({
      agentId: z.string(),
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(50).default(10)
    }))
    .query(async ({ input, ctx }) => {
      const { agentId, page, limit } = input
      const skip = (page - 1) * limit

      const executions = await ctx.db.agentExecution.findMany({
        where: {
          agentId,
          tenantId: ctx.session.user.tenantId
        },
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          user: {
            select: { name: true, email: true }
          }
        }
      })

      return executions
    })
})
```

## 🔗 SGASpace 与 CAMEL 的集成点

### 1. 工作流与 CAMEL 集成

```typescript
// server/workflow/camel-workflow-executor.ts
export class CAMELWorkflowExecutor {
  private camelClient: CAMELServiceClient

  constructor() {
    this.camelClient = new CAMELServiceClient()
  }

  async executeWorkflowStep(
    workflowId: string,
    stepId: string,
    stepConfig: WorkflowStepConfig,
    input: any,
    tenantId: string
  ): Promise<WorkflowStepResult> {

    // 1. 分析步骤中的智能体节点
    const agentNodes = this.extractAgentNodes(stepConfig)

    if (agentNodes.length === 0) {
      throw new Error('No agent nodes found in workflow step')
    }

    // 2. 单智能体执行
    if (agentNodes.length === 1) {
      const agentNode = agentNodes[0]
      const agent = await this.getAgent(agentNode.agentId, tenantId)

      const result = await this.camelClient.executeAgent({
        agentId: agent.id,
        agentType: agent.type,
        input: { message: input.message || input.task },
        context: input.context || {},
        tenantId,
        userId: input.userId
      })

      return {
        success: result.success,
        output: result.output,
        logs: result.logs || [],
        executionTime: result.executionTime || 0,
        error: result.error
      }
    }

    // 3. 多智能体协作执行
    const agentConfigs = await Promise.all(
      agentNodes.map(async (node) => {
        const agent = await this.getAgent(node.agentId, tenantId)
        return {
          id: agent.id,
          name: agent.name,
          type: agent.type,
          description: agent.description,
          role: node.role || agent.name
        }
      })
    )

    const result = await this.camelClient.executeWorkflowStep({
      workflowId,
      stepId,
      agentConfigs,
      input,
      tenantId
    })

    return {
      success: result.success,
      output: result.results,
      logs: result.stepLogs || [],
      executionTime: this.calculateTotalExecutionTime(result.stepLogs || []),
      error: result.error
    }
  }

  private extractAgentNodes(stepConfig: WorkflowStepConfig): AgentNode[] {
    // 从工作流步骤配置中提取智能体节点
    const nodes: AgentNode[] = []

    if (stepConfig.nodes) {
      for (const node of stepConfig.nodes) {
        if (node.type === 'agent' && node.data?.agentId) {
          nodes.push({
            agentId: node.data.agentId,
            role: node.data.role,
            config: node.data.config || {}
          })
        }
      }
    }

    return nodes
  }

  private async getAgent(agentId: string, tenantId: string): Promise<Agent> {
    const agent = await prisma.agent.findUnique({
      where: {
        id: agentId,
        tenantId
      }
    })

    if (!agent) {
      throw new Error(`Agent not found: ${agentId}`)
    }

    return agent
  }

  private calculateTotalExecutionTime(stepLogs: any[]): number {
    return stepLogs.reduce((total, log) => {
      return total + (log.execution_time || 0)
    }, 0)
  }
}

// 类型定义
interface WorkflowStepConfig {
  nodes?: Array<{
    type: string
    data?: {
      agentId?: string
      role?: string
      config?: any
    }
  }>
}

interface AgentNode {
  agentId: string
  role?: string
  config: any
}

interface WorkflowStepResult {
  success: boolean
  output?: any
  logs: any[]
  executionTime: number
  error?: string
}
```

### 2. 部署配置

```dockerfile
# camel_service/Dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

```yaml
# docker-compose.yml (更新)
version: '3.8'
services:
  web:
    build: .
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=********************************************/sgaspace
      - REDIS_URL=redis://redis:6379
      - CAMEL_SERVICE_URL=http://camel-service:8000
      - NEXTAUTH_SECRET=your-secret-key
      - NEXTAUTH_URL=http://localhost:3000
    depends_on:
      - postgres
      - redis
      - camel-service
    networks:
      - sgaspace-network

  camel-service:
    build: ./camel_service
    ports:
      - "8000:8000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - DATABASE_URL=********************************************/sgaspace
    depends_on:
      - postgres
    networks:
      - sgaspace-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  postgres:
    image: pgvector/pgvector:pg15
    environment:
      - POSTGRES_DB=sgaspace
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - sgaspace-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    networks:
      - sgaspace-network
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:

networks:
  sgaspace-network:
    driver: bridge
```

```python
# camel_service/requirements.txt
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
camel-ai==*******
openai==1.3.0
python-multipart==0.0.6
httpx==0.25.2
asyncio==3.4.3
```

### 3. 错误处理与重试机制

```typescript
// lib/camel-client.ts (增强版)
export class CAMELServiceClient {
  private baseUrl: string
  private maxRetries: number = 3
  private retryDelay: number = 1000 // 1秒

  constructor() {
    this.baseUrl = process.env.CAMEL_SERVICE_URL || 'http://localhost:8000'
  }

  private async withRetry<T>(
    operation: () => Promise<T>,
    context: string
  ): Promise<T> {
    let lastError: Error

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        return await operation()
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error))

        if (attempt === this.maxRetries) {
          console.error(`${context} failed after ${this.maxRetries} attempts:`, lastError)
          break
        }

        console.warn(`${context} attempt ${attempt} failed, retrying in ${this.retryDelay}ms...`)
        await this.delay(this.retryDelay * attempt) // 指数退避
      }
    }

    throw lastError!
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  async executeAgentWithRetry(request: {
    agentId: string
    agentType: string
    input: any
    context?: any
    tenantId: string
    userId: string
  }): Promise<{
    success: boolean
    output?: any
    logs?: string[]
    tokensUsed?: number
    executionTime?: number
    error?: string
  }> {
    return this.withRetry(async () => {
      const response = await fetch(`${this.baseUrl}/agents/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Request-Timeout': '30000', // 30秒超时
        },
        body: JSON.stringify({
          agent_id: request.agentId,
          agent_type: request.agentType,
          input: request.input,
          context: request.context || {},
          tenant_id: request.tenantId,
          user_id: request.userId
        }),
        signal: AbortSignal.timeout(30000) // 30秒超时
      })

      if (!response.ok) {
        if (response.status >= 500) {
          throw new Error(`CAMEL service error: ${response.status}`)
        } else {
          // 4xx错误不重试
          const errorData = await response.json().catch(() => ({}))
          throw new Error(`Client error: ${response.status} - ${errorData.detail || 'Unknown error'}`)
        }
      }

      return await response.json()
    }, `CAMEL agent execution (${request.agentId})`)
  }

  async executeWorkflowStepWithRetry(request: {
    workflowId: string
    stepId: string
    agentConfigs: any[]
    input: any
    tenantId: string
  }): Promise<{
    success: boolean
    results?: any
    stepLogs?: any[]
    error?: string
  }> {
    return this.withRetry(async () => {
      const response = await fetch(`${this.baseUrl}/workflows/step`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Request-Timeout': '60000', // 60秒超时（工作流可能更复杂）
        },
        body: JSON.stringify({
          workflow_id: request.workflowId,
          step_id: request.stepId,
          agent_configs: request.agentConfigs,
          input: request.input,
          tenant_id: request.tenantId
        }),
        signal: AbortSignal.timeout(60000)
      })

      if (!response.ok) {
        if (response.status >= 500) {
          throw new Error(`CAMEL service error: ${response.status}`)
        } else {
          const errorData = await response.json().catch(() => ({}))
          throw new Error(`Client error: ${response.status} - ${errorData.detail || 'Unknown error'}`)
        }
      }

      return await response.json()
    }, `CAMEL workflow step execution (${request.workflowId}/${request.stepId})`)
  }

  async healthCheckWithTimeout(timeoutMs: number = 5000): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/health`, {
        signal: AbortSignal.timeout(timeoutMs)
      })
      return response.ok
    } catch (error) {
      console.error('CAMEL service health check failed:', error)
      return false
    }
  }
}

// 增强的单例实例
export const camelClient = new CAMELServiceClient()

// 健康检查中间件
export async function ensureCAMELServiceHealth(): Promise<void> {
  const isHealthy = await camelClient.healthCheckWithTimeout(5000)

  if (!isHealthy) {
    throw new Error('CAMEL service is not available')
  }
}
```

## 📊 CAMEL 独立服务集成优势

### 1. 架构优势
- **服务隔离** - CAMEL服务独立运行，故障不影响Web应用
- **技术栈纯净** - Python AI生态与Node.js Web生态各自发挥优势
- **独立扩缩容** - 可根据AI推理负载单独调整资源
- **版本管理** - CAMEL服务可独立升级和回滚

### 2. 专业智能体能力
- **角色扮演** - CAMEL的核心优势，智能体具有明确的角色定义
- **任务分解** - 自动将复杂任务分解为可执行的子任务
- **工具集成** - 丰富的工具包，支持代码执行、搜索、分析等
- **记忆管理** - 智能体具有上下文记忆和学习能力

### 3. 多智能体协作
- **Workforce模式** - 多个智能体协同工作
- **RolePlaying模式** - 两个智能体角色扮演对话
- **任务分配** - 自动任务分配和负载均衡
- **质量控制** - 内置的批评者智能体进行质量检查

### 4. 企业级特性
- **可扩展性** - 支持大规模智能体部署
- **可观测性** - 完整的执行日志和监控
- **容错性** - 智能体故障恢复和重试机制
- **安全性** - 智能体权限控制和沙箱执行

## 🔄 集成后的架构优势

通过独立服务集成CAMEL框架，SGASpace获得了：

1. **专业AI能力** - 基于CAMEL的成熟智能体框架
2. **架构清晰** - Web应用与AI服务职责分离
3. **运维友好** - 独立部署、监控、扩缩容
4. **技术灵活** - 可以轻松替换或升级AI服务

### 更新后的技术栈

```
前端: Next.js 15 + TypeScript + React Flow
API: tRPC + NextAuth.js + Redis Pub/Sub
智能体服务: FastAPI + CAMEL Framework (Python)
数据库: PostgreSQL + pgvector + Redis
对象存储: MinIO/S3
部署: Docker + Docker Compose
```

## 🚀 快速开始

### 1. 启动开发环境
```bash
# 克隆项目
git clone <repository-url>
cd sgaspace

# 启动所有服务
docker-compose up -d

# 检查服务状态
curl http://localhost:8000/health  # CAMEL服务
curl http://localhost:3000/api/health  # Web应用
```

### 2. 创建第一个智能体
```typescript
// 通过tRPC创建智能体
const agent = await trpc.agent.create.mutate({
  name: "任务规划助手",
  description: "专业的任务分解和规划智能体",
  type: "TASK_PLANNING"
})

// 执行智能体任务
const result = await trpc.agent.execute.mutate({
  agentId: agent.id,
  input: {
    message: "请帮我制定一个产品发布的详细计划"
  }
})
```

---

**文档版本**: 2.0.0
**最后更新**: 2024-12-19
**CAMEL集成版本**: v2.0 (独立服务)
