# SGASpace CAMEL 框架集成设计

## 🐪 CAMEL 框架概述

CAMEL (Communicative Agents for "Mind" Exploration of Large Scale Language Model Society) 是一个强大的多智能体协作框架。我们将CAMEL作为SGASpace的**核心智能体引擎**，提供专业的多智能体协作能力。

### CAMEL 核心优势

1. **角色扮演机制** - 智能体具有明确的角色定义和专业能力
2. **多智能体协作** - 支持复杂的智能体间协作模式
3. **任务分解能力** - 自动将复杂任务分解为子任务
4. **工具集成** - 丰富的工具包，支持代码执行、搜索等
5. **记忆管理** - 智能体具有上下文记忆能力
6. **可观测性** - 完整的执行日志和状态监控

## 🏗️ CAMEL 集成架构

### 整体集成架构

```mermaid
graph TB
    subgraph "SGASpace 前端层"
        A[Next.js 工作流设计器]
        B[智能体管理界面]
        C[实时监控面板]
    end
    
    subgraph "SGASpace API 层"
        D[tRPC API Routes]
        E[WebSocket 服务]
        F[认证中间件]
    end
    
    subgraph "SGASpace 业务层"
        G[工作流引擎]
        H[智能体管理服务]
        I[任务调度器]
    end
    
    subgraph "CAMEL 框架层"
        J[CAMEL Workforce]
        K[CAMEL RolePlaying]
        L[CAMEL ChatAgent]
        M[CAMEL TaskAgent]
        N[CAMEL ToolkitAgent]
    end
    
    subgraph "CAMEL 工具层"
        O[SearchToolkit]
        P[CodeExecutionToolkit]
        Q[TaskPlanningToolkit]
        R[ThinkingToolkit]
    end
    
    subgraph "数据存储层"
        S[PostgreSQL]
        T[Redis]
        U[Qdrant]
        V[MinIO]
    end
    
    A --> D
    B --> E
    C --> F
    D --> G
    E --> H
    F --> I
    G --> J
    H --> K
    I --> L
    J --> M
    K --> N
    L --> O
    M --> P
    N --> Q
    O --> R
    P --> S
    Q --> T
    R --> U
```

### CAMEL 智能体类型映射

| SGASpace 智能体类型 | CAMEL 对应实现 | 主要能力 |
|-------------------|---------------|---------|
| **任务规划智能体** | TaskPlannerAgent + Workforce | 任务分解、计划制定、资源分配 |
| **知识管理智能体** | ChatAgent + SearchToolkit | 知识检索、内容生成、信息整理 |
| **决策支持智能体** | ChatAgent + ThinkingToolkit | 数据分析、风险评估、决策建议 |
| **工具集成智能体** | ToolkitAgent + 自定义工具 | API调用、系统集成、自动化操作 |
| **协作协调智能体** | RolePlaying + CriticAgent | 智能体协调、质量控制、流程优化 |

## 🔧 CAMEL 集成实现

### 1. CAMEL 智能体包装器

```typescript
// server/camel/camel-agent-wrapper.ts
import { spawn } from 'child_process'
import { EventEmitter } from 'events'

export class CAMELAgentWrapper extends EventEmitter {
  private pythonProcess: any
  private agentId: string
  private agentType: string
  private config: any
  
  constructor(agentId: string, agentType: string, config: any) {
    super()
    this.agentId = agentId
    this.agentType = agentType
    this.config = config
  }
  
  async initialize(): Promise<void> {
    // 启动Python CAMEL智能体进程
    this.pythonProcess = spawn('python', [
      '-m', 'camel.agents.chat_agent',
      '--agent-id', this.agentId,
      '--agent-type', this.agentType,
      '--config', JSON.stringify(this.config)
    ], {
      stdio: ['pipe', 'pipe', 'pipe'],
      cwd: process.cwd() + '/camel'
    })
    
    // 设置进程通信
    this.setupProcessCommunication()
    
    // 等待智能体初始化完成
    await this.waitForInitialization()
  }
  
  async execute(input: any): Promise<any> {
    return new Promise((resolve, reject) => {
      const requestId = this.generateRequestId()
      
      // 发送执行请求
      this.sendToProcess({
        type: 'execute',
        requestId,
        input
      })
      
      // 监听响应
      const timeout = setTimeout(() => {
        reject(new Error('Agent execution timeout'))
      }, 30000) // 30秒超时
      
      this.once(`response:${requestId}`, (response) => {
        clearTimeout(timeout)
        if (response.error) {
          reject(new Error(response.error))
        } else {
          resolve(response.result)
        }
      })
    })
  }
  
  private setupProcessCommunication(): void {
    this.pythonProcess.stdout.on('data', (data: Buffer) => {
      try {
        const message = JSON.parse(data.toString())
        this.handleProcessMessage(message)
      } catch (error) {
        console.error('Failed to parse process message:', error)
      }
    })
    
    this.pythonProcess.stderr.on('data', (data: Buffer) => {
      console.error('CAMEL Agent Error:', data.toString())
    })
    
    this.pythonProcess.on('exit', (code: number) => {
      console.log(`CAMEL Agent process exited with code ${code}`)
      this.emit('agent:exited', { agentId: this.agentId, code })
    })
  }
  
  private handleProcessMessage(message: any): void {
    switch (message.type) {
      case 'initialized':
        this.emit('agent:initialized')
        break
      case 'response':
        this.emit(`response:${message.requestId}`, message)
        break
      case 'status_update':
        this.emit('agent:status', message.status)
        break
      case 'log':
        this.emit('agent:log', message)
        break
    }
  }
  
  private sendToProcess(message: any): void {
    this.pythonProcess.stdin.write(JSON.stringify(message) + '\n')
  }
  
  async destroy(): Promise<void> {
    if (this.pythonProcess) {
      this.pythonProcess.kill('SIGTERM')
      this.pythonProcess = null
    }
  }
}
```

### 2. CAMEL 智能体服务

```typescript
// server/services/camel-agent.service.ts
export class CAMELAgentService {
  private agents: Map<string, CAMELAgentWrapper> = new Map()
  private agentPool: Map<string, CAMELAgentWrapper[]> = new Map()
  
  async createAgent(agentConfig: AgentConfig): Promise<string> {
    const agentId = generateId()
    const wrapper = new CAMELAgentWrapper(agentId, agentConfig.type, agentConfig)
    
    // 初始化CAMEL智能体
    await wrapper.initialize()
    
    // 注册事件监听
    this.setupAgentEventHandlers(wrapper)
    
    // 存储智能体实例
    this.agents.set(agentId, wrapper)
    
    // 更新数据库状态
    await this.updateAgentStatus(agentId, 'ACTIVE')
    
    return agentId
  }
  
  async executeAgent(agentId: string, input: any): Promise<any> {
    const agent = this.agents.get(agentId)
    if (!agent) {
      throw new Error(`Agent not found: ${agentId}`)
    }
    
    try {
      const result = await agent.execute(input)
      
      // 记录执行日志
      await this.logExecution(agentId, input, result, 'SUCCESS')
      
      return result
    } catch (error) {
      // 记录错误日志
      await this.logExecution(agentId, input, null, 'ERROR', error.message)
      throw error
    }
  }
  
  async createWorkforce(workforceConfig: WorkforceConfig): Promise<string> {
    const workforceId = generateId()
    
    // 创建CAMEL Workforce实例
    const workforce = new CAMELWorkforceWrapper(workforceId, workforceConfig)
    await workforce.initialize()
    
    // 存储workforce实例
    this.workforces.set(workforceId, workforce)
    
    return workforceId
  }
  
  async executeWorkforce(workforceId: string, task: string): Promise<any> {
    const workforce = this.workforces.get(workforceId)
    if (!workforce) {
      throw new Error(`Workforce not found: ${workforceId}`)
    }
    
    return await workforce.execute(task)
  }
  
  private setupAgentEventHandlers(agent: CAMELAgentWrapper): void {
    agent.on('agent:status', async (status) => {
      await this.updateAgentStatus(agent.agentId, status)
      
      // 广播状态更新
      this.eventBus.emit('agent:status:updated', {
        agentId: agent.agentId,
        status
      })
    })
    
    agent.on('agent:log', async (log) => {
      await this.saveAgentLog(agent.agentId, log)
    })
    
    agent.on('agent:exited', async ({ agentId, code }) => {
      await this.handleAgentExit(agentId, code)
    })
  }
}
```

### 3. CAMEL Workforce 集成

```typescript
// server/camel/camel-workforce-wrapper.ts
export class CAMELWorkforceWrapper {
  private workforceId: string
  private pythonProcess: any
  private config: WorkforceConfig
  
  constructor(workforceId: string, config: WorkforceConfig) {
    this.workforceId = workforceId
    this.config = config
  }
  
  async initialize(): Promise<void> {
    // 启动CAMEL Workforce Python进程
    this.pythonProcess = spawn('python', [
      '-c', `
import asyncio
from camel.societies.workforce import Workforce
from camel.agents import ChatAgent
from camel.models import ModelFactory

# 创建Workforce实例
workforce = Workforce('${this.workforceId}')

# 添加工作者
${this.generateWorkersCode()}

# 启动Workforce
asyncio.run(workforce.run())
      `
    ], {
      stdio: ['pipe', 'pipe', 'pipe'],
      cwd: process.cwd() + '/camel'
    })
    
    this.setupProcessCommunication()
  }
  
  async execute(task: string): Promise<any> {
    return new Promise((resolve, reject) => {
      const requestId = this.generateRequestId()
      
      // 发送任务执行请求
      this.sendToProcess({
        type: 'execute_task',
        requestId,
        task
      })
      
      // 监听执行结果
      this.once(`task_completed:${requestId}`, (result) => {
        resolve(result)
      })
      
      this.once(`task_failed:${requestId}`, (error) => {
        reject(new Error(error))
      })
    })
  }
  
  private generateWorkersCode(): string {
    return this.config.workers.map(worker => `
# 创建${worker.role}智能体
${worker.role.toLowerCase()}_agent = ChatAgent(
    system_message="${worker.systemMessage}",
    model=ModelFactory.create(
        model_platform=ModelPlatformType.${worker.modelPlatform},
        model_type=ModelType.${worker.modelType}
    )
)

# 添加到workforce
workforce.add_single_agent_worker(
    "${worker.role}",
    worker=${worker.role.toLowerCase()}_agent
)
    `).join('\n')
  }
}
```

### 4. CAMEL 角色扮演集成

```typescript
// server/camel/role-playing-service.ts
export class CAMELRolePlayingService {
  async createRolePlayingSession(config: RolePlayingConfig): Promise<string> {
    const sessionId = generateId()
    
    // 创建Python角色扮演会话
    const pythonScript = `
import asyncio
from camel.societies import RolePlaying
from camel.agents import ChatAgent
from camel.models import ModelFactory
from camel.types import TaskType

# 创建角色扮演会话
role_playing = RolePlaying(
    assistant_role_name="${config.assistantRole}",
    user_role_name="${config.userRole}",
    task_prompt="${config.taskPrompt}",
    with_task_specify=${config.withTaskSpecify},
    with_task_planner=${config.withTaskPlanner},
    with_critic_in_the_loop=${config.withCritic}
)

# 初始化会话
role_playing.init_chat()

# 开始对话循环
async def run_conversation():
    chat_turn_limit = ${config.chatTurnLimit || 10}
    
    for i in range(chat_turn_limit):
        assistant_response, user_response = role_playing.step()
        
        # 发送响应到SGASpace
        print(f"ASSISTANT: {assistant_response.msg.content}")
        print(f"USER: {user_response.msg.content}")
        
        if assistant_response.terminated or user_response.terminated:
            break
    
    return role_playing.get_chat_history()

# 运行对话
result = asyncio.run(run_conversation())
print(f"RESULT: {result}")
    `
    
    // 执行Python脚本
    const result = await this.executePythonScript(pythonScript)
    
    // 保存会话记录
    await this.saveRolePlayingSession(sessionId, config, result)
    
    return sessionId
  }
  
  async getRolePlayingHistory(sessionId: string): Promise<RolePlayingHistory> {
    return await this.db.rolePlayingSession.findUnique({
      where: { id: sessionId },
      include: {
        messages: {
          orderBy: { createdAt: 'asc' }
        }
      }
    })
  }
}
```

## 🔗 SGASpace 与 CAMEL 的集成点

### 1. 智能体生命周期管理

```typescript
// server/agents/camel-agent-manager.ts
export class CAMELAgentManager {
  private camelService: CAMELAgentService
  
  async createSGAAgent(agentData: CreateAgentInput, tenantId: string): Promise<Agent> {
    // 1. 在SGASpace数据库中创建智能体记录
    const agent = await this.db.agent.create({
      data: {
        ...agentData,
        tenantId,
        status: 'INITIALIZING'
      }
    })
    
    // 2. 创建对应的CAMEL智能体实例
    const camelConfig = this.mapToCAMELConfig(agentData)
    const camelAgentId = await this.camelService.createAgent(camelConfig)
    
    // 3. 关联SGASpace智能体和CAMEL智能体
    await this.db.agent.update({
      where: { id: agent.id },
      data: {
        config: {
          ...agent.config,
          camelAgentId
        },
        status: 'ACTIVE'
      }
    })
    
    return agent
  }
  
  async executeSGAAgent(agentId: string, input: any): Promise<any> {
    // 1. 获取SGASpace智能体信息
    const agent = await this.db.agent.findUnique({
      where: { id: agentId }
    })
    
    if (!agent) {
      throw new Error(`Agent not found: ${agentId}`)
    }
    
    // 2. 获取关联的CAMEL智能体ID
    const camelAgentId = agent.config.camelAgentId
    
    // 3. 执行CAMEL智能体
    const result = await this.camelService.executeAgent(camelAgentId, input)
    
    // 4. 记录执行历史
    await this.recordExecution(agentId, input, result)
    
    return result
  }
  
  private mapToCAMELConfig(agentData: CreateAgentInput): CAMELAgentConfig {
    const baseConfig = {
      role_name: agentData.name,
      model_platform: 'OPENAI',
      model_type: 'GPT_4O_MINI'
    }
    
    switch (agentData.type) {
      case 'TASK_PLANNING':
        return {
          ...baseConfig,
          agent_class: 'TaskPlannerAgent',
          tools: ['TaskPlanningToolkit', 'ThinkingToolkit']
        }
      case 'KNOWLEDGE_MANAGEMENT':
        return {
          ...baseConfig,
          agent_class: 'ChatAgent',
          tools: ['SearchToolkit', 'RetrievalToolkit']
        }
      case 'DECISION_SUPPORT':
        return {
          ...baseConfig,
          agent_class: 'ChatAgent',
          tools: ['ThinkingToolkit', 'AnalysisToolkit']
        }
      default:
        return {
          ...baseConfig,
          agent_class: 'ChatAgent'
        }
    }
  }
}
```

### 2. 工作流与CAMEL Workforce集成

```typescript
// server/workflow/camel-workflow-executor.ts
export class CAMELWorkflowExecutor {
  private camelService: CAMELAgentService
  
  async executeWorkflow(workflowId: string, input: any): Promise<WorkflowExecution> {
    // 1. 加载工作流定义
    const workflow = await this.loadWorkflow(workflowId)
    
    // 2. 分析工作流中的智能体节点
    const agentNodes = this.extractAgentNodes(workflow)
    
    // 3. 创建CAMEL Workforce配置
    const workforceConfig = this.createWorkforceConfig(agentNodes)
    
    // 4. 创建CAMEL Workforce实例
    const workforceId = await this.camelService.createWorkforce(workforceConfig)
    
    // 5. 执行工作流任务
    const result = await this.camelService.executeWorkforce(workforceId, input)
    
    // 6. 记录执行结果
    await this.recordWorkflowExecution(workflowId, input, result)
    
    return result
  }
  
  private createWorkforceConfig(agentNodes: WorkflowNode[]): WorkforceConfig {
    const workers = agentNodes.map(node => ({
      role: node.data.agent.name,
      systemMessage: this.generateSystemMessage(node.data.agent),
      modelPlatform: 'OPENAI',
      modelType: 'GPT_4O_MINI',
      tools: this.mapAgentTools(node.data.agent.type)
    }))
    
    return {
      workers,
      maxWorkers: workers.length,
      taskTimeout: 300 // 5分钟
    }
  }
  
  private generateSystemMessage(agent: Agent): string {
    return `
你是一个专业的${agent.name}智能体。

角色描述：${agent.description}

主要职责：
${this.getAgentResponsibilities(agent.type)}

工作方式：
1. 仔细分析接收到的任务
2. 使用你的专业知识和工具
3. 提供准确、有用的结果
4. 与其他智能体协作完成复杂任务

请始终保持专业、准确、有帮助的态度。
    `
  }
  
  private getAgentResponsibilities(agentType: AgentType): string {
    const responsibilities = {
      TASK_PLANNING: `
- 分析复杂任务并分解为子任务
- 制定详细的执行计划
- 分配任务给合适的智能体
- 监控任务执行进度
      `,
      KNOWLEDGE_MANAGEMENT: `
- 检索相关知识和信息
- 整理和总结文档内容
- 回答基于知识库的问题
- 管理和组织知识资产
      `,
      DECISION_SUPPORT: `
- 分析数据和信息
- 评估风险和机会
- 提供决策建议
- 生成分析报告
      `
    }
    
    return responsibilities[agentType] || '执行分配的任务'
  }
}
```

### 3. Python CAMEL 服务接口

```python
# server/camel/camel_service.py
import asyncio
import json
import sys
from typing import Dict, Any, Optional
from camel.agents import ChatAgent, TaskPlannerAgent
from camel.societies import RolePlaying
from camel.societies.workforce import Workforce
from camel.models import ModelFactory
from camel.types import ModelPlatformType, ModelType, TaskType
from camel.toolkits import (
    SearchToolkit, 
    CodeExecutionToolkit, 
    TaskPlanningToolkit,
    ThinkingToolkit
)

class SGASpaceCAMELService:
    """SGASpace与CAMEL框架的集成服务"""
    
    def __init__(self):
        self.agents: Dict[str, ChatAgent] = {}
        self.workforces: Dict[str, Workforce] = {}
        self.role_playing_sessions: Dict[str, RolePlaying] = {}
    
    async def create_agent(self, agent_config: Dict[str, Any]) -> str:
        """创建CAMEL智能体实例"""
        agent_id = agent_config['id']
        agent_type = agent_config['type']
        
        # 根据类型创建不同的智能体
        if agent_type == 'TASK_PLANNING':
            agent = TaskPlannerAgent(
                model=ModelFactory.create(
                    model_platform=ModelPlatformType.OPENAI,
                    model_type=ModelType.GPT_4O_MINI
                )
            )
        else:
            # 创建通用ChatAgent
            system_message = self.generate_system_message(agent_config)
            agent = ChatAgent(
                system_message=system_message,
                model=ModelFactory.create(
                    model_platform=ModelPlatformType.OPENAI,
                    model_type=ModelType.GPT_4O_MINI
                )
            )
            
            # 添加工具包
            tools = self.get_agent_tools(agent_type)
            for tool in tools:
                agent.add_toolkit(tool)
        
        self.agents[agent_id] = agent
        return agent_id
    
    async def execute_agent(self, agent_id: str, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行智能体任务"""
        if agent_id not in self.agents:
            raise ValueError(f"Agent not found: {agent_id}")
        
        agent = self.agents[agent_id]
        
        try:
            # 执行智能体
            response = agent.step(input_data['message'])
            
            return {
                'success': True,
                'result': {
                    'content': response.msg.content,
                    'role': response.msg.role_name,
                    'metadata': response.info
                }
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    async def create_workforce(self, workforce_config: Dict[str, Any]) -> str:
        """创建CAMEL Workforce实例"""
        workforce_id = workforce_config['id']
        
        # 创建Workforce
        workforce = Workforce(workforce_id)
        
        # 添加工作者
        for worker_config in workforce_config['workers']:
            agent = ChatAgent(
                system_message=worker_config['system_message'],
                model=ModelFactory.create(
                    model_platform=ModelPlatformType.OPENAI,
                    model_type=ModelType.GPT_4O_MINI
                )
            )
            
            workforce.add_single_agent_worker(
                worker_config['role'],
                worker=agent
            )
        
        self.workforces[workforce_id] = workforce
        return workforce_id
    
    async def execute_workforce_task(self, workforce_id: str, task: str) -> Dict[str, Any]:
        """执行Workforce任务"""
        if workforce_id not in self.workforces:
            raise ValueError(f"Workforce not found: {workforce_id}")
        
        workforce = self.workforces[workforce_id]
        
        try:
            # 执行任务
            result = await workforce.process_task(task)
            
            return {
                'success': True,
                'result': result,
                'task_history': workforce.get_task_history()
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def generate_system_message(self, agent_config: Dict[str, Any]) -> str:
        """生成智能体系统消息"""
        role_templates = {
            'KNOWLEDGE_MANAGEMENT': """
你是一个专业的知识管理专家。你的主要职责是：
1. 检索和整理相关知识信息
2. 回答基于知识库的问题
3. 总结和分析文档内容
4. 提供准确的信息支持

请始终基于可靠的信息源提供答案。
            """,
            'DECISION_SUPPORT': """
你是一个专业的决策支持分析师。你的主要职责是：
1. 分析数据和信息
2. 评估风险和机会
3. 提供客观的决策建议
4. 生成详细的分析报告

请确保你的分析客观、全面、有据可依。
            """,
            'TOOL_INTEGRATION': """
你是一个专业的工具集成专家。你的主要职责是：
1. 调用各种外部工具和API
2. 处理工具返回的数据
3. 整合多个工具的结果
4. 提供自动化解决方案

请确保工具调用的准确性和安全性。
            """
        }
        
        return role_templates.get(
            agent_config['type'], 
            f"你是一个专业的{agent_config['name']}智能体。"
        )
    
    def get_agent_tools(self, agent_type: str) -> list:
        """获取智能体工具包"""
        tool_mapping = {
            'TASK_PLANNING': [TaskPlanningToolkit(), ThinkingToolkit()],
            'KNOWLEDGE_MANAGEMENT': [SearchToolkit()],
            'DECISION_SUPPORT': [ThinkingToolkit()],
            'TOOL_INTEGRATION': [CodeExecutionToolkit()]
        }
        
        return tool_mapping.get(agent_type, [])

# 主服务循环
async def main():
    service = SGASpaceCAMELService()
    
    # 监听来自Node.js的请求
    while True:
        try:
            line = sys.stdin.readline()
            if not line:
                break
                
            request = json.loads(line.strip())
            
            # 处理不同类型的请求
            if request['type'] == 'create_agent':
                result = await service.create_agent(request['config'])
                print(json.dumps({'type': 'agent_created', 'result': result}))
            
            elif request['type'] == 'execute_agent':
                result = await service.execute_agent(request['agent_id'], request['input'])
                print(json.dumps({'type': 'agent_response', 'result': result}))
            
            elif request['type'] == 'create_workforce':
                result = await service.create_workforce(request['config'])
                print(json.dumps({'type': 'workforce_created', 'result': result}))
            
            elif request['type'] == 'execute_workforce':
                result = await service.execute_workforce_task(request['workforce_id'], request['task'])
                print(json.dumps({'type': 'workforce_response', 'result': result}))
                
        except Exception as e:
            print(json.dumps({'type': 'error', 'error': str(e)}))

if __name__ == "__main__":
    asyncio.run(main())
```

## 📊 CAMEL 集成优势

### 1. 专业智能体能力
- **角色扮演** - CAMEL的核心优势，智能体具有明确的角色定义
- **任务分解** - 自动将复杂任务分解为可执行的子任务
- **工具集成** - 丰富的工具包，支持代码执行、搜索、分析等
- **记忆管理** - 智能体具有上下文记忆和学习能力

### 2. 多智能体协作
- **Workforce模式** - 多个智能体协同工作
- **RolePlaying模式** - 两个智能体角色扮演对话
- **任务分配** - 自动任务分配和负载均衡
- **质量控制** - 内置的批评者智能体进行质量检查

### 3. 企业级特性
- **可扩展性** - 支持大规模智能体部署
- **可观测性** - 完整的执行日志和监控
- **容错性** - 智能体故障恢复和重试机制
- **安全性** - 智能体权限控制和沙箱执行

## 🔄 集成后的架构优势

通过集成CAMEL框架，SGASpace获得了：

1. **专业AI能力** - 基于CAMEL的成熟智能体框架
2. **协作模式** - 多种智能体协作模式
3. **工具生态** - 丰富的工具包和扩展能力
4. **学术支持** - 基于学术研究的可靠框架

### 更新后的技术栈

```
前端: Next.js 15 + TypeScript + React Flow
API: tRPC + NextAuth.js + WebSocket
智能体引擎: CAMEL Framework (Python)
数据库: PostgreSQL + Redis + Qdrant + MinIO
部署: Docker + Docker Compose
```

---

**文档版本**: 1.0.0  
**最后更新**: 2024-12-19  
**CAMEL集成版本**: v1.0
