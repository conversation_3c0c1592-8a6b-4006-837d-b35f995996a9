# SGASpace 智能体市场设计

## 🛒 市场概览

SGASpace 智能体市场是一个**开放式生态平台**，允许用户聘用外部开发者制作的专业智能体，同时支持内部智能体的自主创建。通过标准化的接入流程和质量认证体系，为用户提供丰富、可靠的智能体服务。

### 设计理念

1. **开放生态** - 支持第三方开发者贡献智能体
2. **质量优先** - 严格的认证和评价体系
3. **用户友好** - 简单的聘用和集成流程
4. **商业可持续** - 合理的收益分成模式
5. **安全可控** - 沙箱执行和权限管理

## 🏗️ 市场架构设计

### 整体架构图

```mermaid
graph TB
    subgraph "SGASpace 核心平台"
        A[智能体管理]
        B[工作流引擎]
        C[用户系统]
        D[支付系统]
    end
    
    subgraph "智能体市场模块"
        E[市场前端界面]
        F[市场API网关]
        G[智能体注册服务]
        H[评价与评论系统]
        I[搜索与推荐引擎]
    end
    
    subgraph "外部智能体生态"
        J[官方智能体商店]
        K[第三方开发者]
        L[企业定制智能体]
        M[社区贡献智能体]
    end
    
    subgraph "质量保证体系"
        N[智能体验证服务]
        O[沙箱执行环境]
        P[性能监控系统]
        Q[安全扫描工具]
    end
    
    A --> F
    E --> F
    F --> G
    F --> H
    F --> I
    
    J --> G
    K --> G
    L --> G
    M --> G
    
    G --> N
    N --> O
    N --> P
    N --> Q
    
    F --> D
    B --> O
```

## 📊 智能体分类体系

### 按来源分类

```typescript
enum AgentSource {
  INTERNAL = 'internal',        // 平台内置智能体
  MARKETPLACE = 'marketplace',  // 市场聘用智能体
  CUSTOM = 'custom',           // 用户自制智能体
  ENTERPRISE = 'enterprise'    // 企业定制智能体
}

enum AgentProvider {
  OFFICIAL = 'official',       // 官方提供
  VERIFIED = 'verified',       // 认证开发者
  COMMUNITY = 'community',     // 社区贡献
  ENTERPRISE = 'enterprise'    // 企业开发者
}
```

### 按功能分类

```typescript
enum MarketplaceCategory {
  // 业务流程类
  TASK_AUTOMATION = 'task_automation',
  WORKFLOW_OPTIMIZATION = 'workflow_optimization',
  PROJECT_MANAGEMENT = 'project_management',
  
  // 数据分析类
  DATA_ANALYSIS = 'data_analysis',
  BUSINESS_INTELLIGENCE = 'business_intelligence',
  FINANCIAL_ANALYSIS = 'financial_analysis',
  
  // 内容创作类
  CONTENT_GENERATION = 'content_generation',
  TRANSLATION = 'translation',
  COPYWRITING = 'copywriting',
  
  // 客户服务类
  CUSTOMER_SUPPORT = 'customer_support',
  SALES_ASSISTANT = 'sales_assistant',
  CONSULTATION = 'consultation',
  
  // 技术开发类
  CODE_GENERATION = 'code_generation',
  SYSTEM_INTEGRATION = 'system_integration',
  API_TESTING = 'api_testing',
  
  // 行业专用类
  LEGAL_ASSISTANT = 'legal_assistant',
  MEDICAL_ADVISOR = 'medical_advisor',
  EDUCATION_TUTOR = 'education_tutor'
}
```

## 💰 商业模式设计

### 定价模型

```typescript
interface AgentPricing {
  model: PricingModel
  price?: number
  currency: string
  billingCycle?: BillingCycle
  usageLimit?: UsageLimit
  trialPeriod?: TrialPeriod
}

enum PricingModel {
  FREE = 'free',                    // 免费使用
  ONE_TIME = 'one_time',           // 一次性购买
  SUBSCRIPTION = 'subscription',    // 订阅模式
  USAGE_BASED = 'usage_based',     // 按使用量计费
  FREEMIUM = 'freemium'            // 免费+高级功能
}

enum BillingCycle {
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  YEARLY = 'yearly'
}

interface UsageLimit {
  type: 'executions' | 'tokens' | 'time'
  limit: number
  period: 'day' | 'week' | 'month'
}
```

### 收益分成策略

```typescript
interface RevenueSharing {
  platformFee: number      // 平台抽成比例 (15-30%)
  developerShare: number   // 开发者分成比例 (70-85%)
  referralBonus?: number   // 推荐奖励比例 (5%)
  
  // 分层费率
  tierRates: {
    basic: { platformFee: 0.30, developerShare: 0.70 }
    verified: { platformFee: 0.20, developerShare: 0.80 }
    premium: { platformFee: 0.15, developerShare: 0.85 }
  }
}
```

## 🔧 技术实现方案

### 数据库模型扩展

```typescript
// 扩展现有的 Agent 模型
model Agent {
  // ... 现有字段
  
  // 市场相关字段
  source          AgentSource    @default(INTERNAL)
  providerId      String?        // 提供者ID
  marketplaceId   String?        // 市场中的唯一ID
  
  // 定价信息
  pricing         Json?          // AgentPricing 结构
  
  // 市场统计
  downloadCount   Int           @default(0)
  rating          Float?        // 平均评分
  ratingCount     Int           @default(0)
  
  // 认证状态
  verification    Json?         // 认证信息
  
  // 关联关系
  reviews         AgentReview[]
  purchases       AgentPurchase[]
  
  @@map("agents")
}

// 智能体评价模型
model AgentReview {
  id          String   @id @default(cuid())
  agentId     String
  userId      String
  tenantId    String
  
  rating      Int      // 1-5 星评分
  title       String?
  content     String?
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // 关联关系
  agent       Agent    @relation(fields: [agentId], references: [id], onDelete: Cascade)
  user        User     @relation(fields: [userId], references: [id])
  
  @@unique([agentId, userId]) // 每个用户只能评价一次
  @@map("agent_reviews")
}

// 智能体购买记录
model AgentPurchase {
  id            String        @id @default(cuid())
  agentId       String
  userId        String
  tenantId      String
  
  purchaseType  PurchaseType
  amount        Float
  currency      String
  status        PurchaseStatus
  
  // 许可信息
  licenseType   String        // 许可类型
  expiresAt     DateTime?     // 过期时间
  usageLimit    Json?         // 使用限制
  
  createdAt     DateTime      @default(now())
  
  // 关联关系
  agent         Agent         @relation(fields: [agentId], references: [id])
  user          User          @relation(fields: [userId], references: [id])
  
  @@map("agent_purchases")
}

enum PurchaseType {
  FREE_TRIAL
  ONE_TIME
  SUBSCRIPTION
  USAGE_BASED
}

enum PurchaseStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
  EXPIRED
}
```

### tRPC API 路由

```typescript
// server/api/routers/marketplace.ts
export const marketplaceRouter = createTRPCRouter({
  // 浏览市场智能体
  browse: publicProcedure
    .input(z.object({
      category: z.nativeEnum(MarketplaceCategory).optional(),
      search: z.string().optional(),
      provider: z.nativeEnum(AgentProvider).optional(),
      priceRange: z.object({
        min: z.number().optional(),
        max: z.number().optional()
      }).optional(),
      rating: z.number().min(1).max(5).optional(),
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(50).default(20)
    }))
    .query(async ({ input }) => {
      const { category, search, provider, priceRange, rating, page, limit } = input
      const skip = (page - 1) * limit
      
      const where = {
        source: AgentSource.MARKETPLACE,
        status: 'ACTIVE',
        ...(category && { 
          config: { 
            path: ['category'], 
            equals: category 
          } 
        }),
        ...(search && {
          OR: [
            { name: { contains: search, mode: 'insensitive' } },
            { description: { contains: search, mode: 'insensitive' } }
          ]
        }),
        ...(provider && {
          config: {
            path: ['provider'],
            equals: provider
          }
        }),
        ...(rating && { rating: { gte: rating } })
      }
      
      const [agents, total] = await Promise.all([
        prisma.agent.findMany({
          where,
          skip,
          take: limit,
          include: {
            _count: {
              select: { reviews: true, purchases: true }
            }
          },
          orderBy: [
            { rating: 'desc' },
            { downloadCount: 'desc' },
            { createdAt: 'desc' }
          ]
        }),
        prisma.agent.count({ where })
      ])
      
      return {
        agents,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    }),

  // 获取智能体详情
  getDetails: publicProcedure
    .input(z.object({
      agentId: z.string()
    }))
    .query(async ({ input }) => {
      const agent = await prisma.agent.findUnique({
        where: { id: input.agentId },
        include: {
          reviews: {
            include: {
              user: {
                select: { name: true, email: true }
              }
            },
            orderBy: { createdAt: 'desc' },
            take: 10
          },
          _count: {
            select: { reviews: true, purchases: true }
          }
        }
      })
      
      if (!agent) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Agent not found'
        })
      }
      
      return agent
    }),

  // 聘用智能体
  hire: protectedProcedure
    .input(z.object({
      agentId: z.string(),
      licenseType: z.enum(['trial', 'monthly', 'yearly', 'lifetime']),
      paymentMethodId: z.string().optional()
    }))
    .mutation(async ({ input, ctx }) => {
      const { agentId, licenseType, paymentMethodId } = input
      
      // 1. 验证智能体存在且可购买
      const agent = await ctx.db.agent.findUnique({
        where: { id: agentId, source: AgentSource.MARKETPLACE }
      })
      
      if (!agent) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Agent not found or not available for hire'
        })
      }
      
      // 2. 检查用户是否已经购买
      const existingPurchase = await ctx.db.agentPurchase.findFirst({
        where: {
          agentId,
          userId: ctx.userId,
          status: PurchaseStatus.COMPLETED,
          OR: [
            { expiresAt: null }, // 永久许可
            { expiresAt: { gt: new Date() } } // 未过期
          ]
        }
      })
      
      if (existingPurchase) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: 'You already have an active license for this agent'
        })
      }
      
      // 3. 计算价格和过期时间
      const pricing = agent.pricing as AgentPricing
      const { amount, expiresAt } = calculatePurchaseDetails(pricing, licenseType)
      
      // 4. 处理支付（如果需要）
      let paymentResult = null
      if (amount > 0 && paymentMethodId) {
        paymentResult = await processPayment({
          amount,
          currency: pricing.currency,
          paymentMethodId,
          userId: ctx.userId,
          description: `Purchase ${agent.name} - ${licenseType} license`
        })
        
        if (!paymentResult.success) {
          throw new TRPCError({
            code: 'PAYMENT_REQUIRED',
            message: 'Payment failed'
          })
        }
      }
      
      // 5. 创建购买记录
      const purchase = await ctx.db.agentPurchase.create({
        data: {
          agentId,
          userId: ctx.userId,
          tenantId: ctx.tenantId,
          purchaseType: getPurchaseType(licenseType),
          amount,
          currency: pricing.currency,
          status: PurchaseStatus.COMPLETED,
          licenseType,
          expiresAt,
          usageLimit: pricing.usageLimit
        }
      })
      
      // 6. 在用户租户中创建智能体实例
      const userAgent = await ctx.db.agent.create({
        data: {
          name: agent.name,
          description: agent.description,
          type: agent.type,
          config: {
            ...agent.config,
            sourceAgentId: agentId,
            purchaseId: purchase.id
          },
          source: AgentSource.MARKETPLACE,
          providerId: agent.providerId,
          marketplaceId: agent.marketplaceId,
          tenantId: ctx.tenantId,
          createdById: ctx.userId,
          status: 'ACTIVE'
        }
      })
      
      // 7. 更新下载统计
      await ctx.db.agent.update({
        where: { id: agentId },
        data: {
          downloadCount: { increment: 1 }
        }
      })
      
      return {
        success: true,
        purchase,
        agentId: userAgent.id
      }
    }),

  // 评价智能体
  review: protectedProcedure
    .input(z.object({
      agentId: z.string(),
      rating: z.number().min(1).max(5),
      title: z.string().optional(),
      content: z.string().optional()
    }))
    .mutation(async ({ input, ctx }) => {
      const { agentId, rating, title, content } = input
      
      // 验证用户是否购买过该智能体
      const purchase = await ctx.db.agentPurchase.findFirst({
        where: {
          agentId,
          userId: ctx.userId,
          status: PurchaseStatus.COMPLETED
        }
      })
      
      if (!purchase) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'You must purchase this agent before reviewing it'
        })
      }
      
      // 创建或更新评价
      const review = await ctx.db.agentReview.upsert({
        where: {
          agentId_userId: {
            agentId,
            userId: ctx.userId
          }
        },
        create: {
          agentId,
          userId: ctx.userId,
          tenantId: ctx.tenantId,
          rating,
          title,
          content
        },
        update: {
          rating,
          title,
          content,
          updatedAt: new Date()
        }
      })
      
      // 更新智能体平均评分
      await updateAgentRating(agentId)
      
      return review
    })
})

// 辅助函数
async function updateAgentRating(agentId: string) {
  const stats = await prisma.agentReview.aggregate({
    where: { agentId },
    _avg: { rating: true },
    _count: { rating: true }
  })
  
  await prisma.agent.update({
    where: { id: agentId },
    data: {
      rating: stats._avg.rating,
      ratingCount: stats._count.rating
    }
  })
}
```

```

## 🎨 前端界面设计

### 市场主页

```typescript
// app/marketplace/page.tsx
export default function MarketplacePage() {
  const [category, setCategory] = useState<MarketplaceCategory>()
  const [search, setSearch] = useState('')
  const [priceFilter, setPriceFilter] = useState<'free' | 'paid' | 'all'>('all')

  const { data: agents, isLoading } = api.marketplace.browse.useQuery({
    category,
    search,
    page: 1,
    limit: 20
  })

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4">智能体市场</h1>
        <p className="text-muted-foreground">
          发现和聘用专业的AI智能体，提升您的工作效率
        </p>
      </div>

      {/* 搜索和筛选 */}
      <div className="mb-8 space-y-4">
        <div className="flex gap-4">
          <Input
            placeholder="搜索智能体..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="flex-1"
          />
          <Select value={category} onValueChange={setCategory}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="选择分类" />
            </SelectTrigger>
            <SelectContent>
              {Object.values(MarketplaceCategory).map((cat) => (
                <SelectItem key={cat} value={cat}>
                  {getCategoryDisplayName(cat)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex gap-2">
          <Badge
            variant={priceFilter === 'all' ? 'default' : 'outline'}
            className="cursor-pointer"
            onClick={() => setPriceFilter('all')}
          >
            全部
          </Badge>
          <Badge
            variant={priceFilter === 'free' ? 'default' : 'outline'}
            className="cursor-pointer"
            onClick={() => setPriceFilter('free')}
          >
            免费
          </Badge>
          <Badge
            variant={priceFilter === 'paid' ? 'default' : 'outline'}
            className="cursor-pointer"
            onClick={() => setPriceFilter('paid')}
          >
            付费
          </Badge>
        </div>
      </div>

      {/* 智能体网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {agents?.agents.map((agent) => (
          <AgentCard key={agent.id} agent={agent} />
        ))}
      </div>
    </div>
  )
}
```

### 智能体卡片组件

```typescript
// components/marketplace/agent-card.tsx
interface AgentCardProps {
  agent: MarketplaceAgent
}

export function AgentCard({ agent }: AgentCardProps) {
  const pricing = agent.pricing as AgentPricing

  return (
    <Card className="h-full flex flex-col hover:shadow-lg transition-shadow">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-2">
            <Avatar className="w-10 h-10">
              <AvatarImage src={agent.config?.avatar} />
              <AvatarFallback>
                {agent.name.slice(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div>
              <CardTitle className="text-lg">{agent.name}</CardTitle>
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <Badge variant="outline" className="text-xs">
                  {getProviderDisplayName(agent.config?.provider)}
                </Badge>
                {agent.verification?.verified && (
                  <Badge variant="secondary" className="text-xs">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    认证
                  </Badge>
                )}
              </div>
            </div>
          </div>
          <div className="text-right">
            {pricing?.model === 'free' ? (
              <Badge variant="secondary">免费</Badge>
            ) : (
              <div className="text-sm">
                <span className="font-semibold">
                  ¥{pricing?.price}
                </span>
                <span className="text-muted-foreground">
                  /{getPricingPeriod(pricing)}
                </span>
              </div>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex-1">
        <p className="text-sm text-muted-foreground mb-4 line-clamp-3">
          {agent.description}
        </p>

        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <div className="flex items-center">
              {[1, 2, 3, 4, 5].map((star) => (
                <Star
                  key={star}
                  className={cn(
                    "w-4 h-4",
                    star <= (agent.rating || 0)
                      ? "fill-yellow-400 text-yellow-400"
                      : "text-gray-300"
                  )}
                />
              ))}
            </div>
            <span className="text-sm text-muted-foreground">
              {agent.rating?.toFixed(1)} ({agent.ratingCount})
            </span>
          </div>

          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <div className="flex items-center gap-1">
              <Download className="w-4 h-4" />
              {agent.downloadCount}
            </div>
            <div className="flex items-center gap-1">
              <Tag className="w-4 h-4" />
              {getCategoryDisplayName(agent.config?.category)}
            </div>
          </div>
        </div>
      </CardContent>

      <CardFooter>
        <div className="flex gap-2 w-full">
          <Button variant="outline" size="sm" className="flex-1">
            <Eye className="w-4 h-4 mr-2" />
            预览
          </Button>
          <Button size="sm" className="flex-1">
            <ShoppingCart className="w-4 h-4 mr-2" />
            聘用
          </Button>
        </div>
      </CardFooter>
    </Card>
  )
}
```

## 🔐 质量保证体系

### 智能体认证流程

```typescript
// server/services/agent-verification.ts
export class AgentVerificationService {
  async submitForVerification(agentId: string, providerId: string): Promise<VerificationRequest> {
    const agent = await prisma.agent.findUnique({
      where: { id: agentId }
    })

    if (!agent) {
      throw new Error('Agent not found')
    }

    // 创建验证请求
    const request = await prisma.verificationRequest.create({
      data: {
        agentId,
        providerId,
        status: 'PENDING',
        submittedAt: new Date(),
        checklistItems: this.generateChecklistItems(agent)
      }
    })

    // 启动自动化检查
    await this.runAutomatedChecks(request.id)

    return request
  }

  private generateChecklistItems(agent: Agent): ChecklistItem[] {
    return [
      {
        id: 'functionality_test',
        name: '功能测试',
        description: '验证智能体基本功能是否正常',
        status: 'PENDING',
        automated: true
      },
      {
        id: 'performance_test',
        name: '性能测试',
        description: '检查响应时间和资源使用',
        status: 'PENDING',
        automated: true
      },
      {
        id: 'security_scan',
        name: '安全扫描',
        description: '检查潜在的安全风险',
        status: 'PENDING',
        automated: true
      },
      {
        id: 'content_review',
        name: '内容审核',
        description: '人工审核智能体描述和示例',
        status: 'PENDING',
        automated: false
      },
      {
        id: 'documentation_check',
        name: '文档检查',
        description: '验证使用说明和API文档完整性',
        status: 'PENDING',
        automated: false
      }
    ]
  }

  private async runAutomatedChecks(requestId: string): Promise<void> {
    const request = await prisma.verificationRequest.findUnique({
      where: { id: requestId },
      include: { agent: true }
    })

    if (!request) return

    // 功能测试
    await this.runFunctionalityTest(request)

    // 性能测试
    await this.runPerformanceTest(request)

    // 安全扫描
    await this.runSecurityScan(request)

    // 更新验证状态
    await this.updateVerificationStatus(requestId)
  }

  private async runFunctionalityTest(request: VerificationRequest): Promise<void> {
    try {
      // 调用智能体执行测试用例
      const testCases = this.generateTestCases(request.agent)
      const results = []

      for (const testCase of testCases) {
        const result = await this.executeTestCase(request.agent, testCase)
        results.push(result)
      }

      const passed = results.every(r => r.success)

      await this.updateChecklistItem(request.id, 'functionality_test', {
        status: passed ? 'PASSED' : 'FAILED',
        details: results,
        completedAt: new Date()
      })

    } catch (error) {
      await this.updateChecklistItem(request.id, 'functionality_test', {
        status: 'FAILED',
        error: error.message,
        completedAt: new Date()
      })
    }
  }

  private async runPerformanceTest(request: VerificationRequest): Promise<void> {
    try {
      const startTime = Date.now()

      // 执行性能测试
      const result = await this.executePerformanceTest(request.agent)

      const endTime = Date.now()
      const responseTime = endTime - startTime

      const passed = responseTime < 10000 && // 10秒内响应
                    result.memoryUsage < 512 * 1024 * 1024 // 512MB内存限制

      await this.updateChecklistItem(request.id, 'performance_test', {
        status: passed ? 'PASSED' : 'FAILED',
        details: {
          responseTime,
          memoryUsage: result.memoryUsage,
          cpuUsage: result.cpuUsage
        },
        completedAt: new Date()
      })

    } catch (error) {
      await this.updateChecklistItem(request.id, 'performance_test', {
        status: 'FAILED',
        error: error.message,
        completedAt: new Date()
      })
    }
  }
}
```

## 📈 实施路线图

### Phase 1: 基础市场功能 (4-6周)

#### 核心功能
- [ ] 市场前端界面开发
- [ ] 智能体浏览和搜索功能
- [ ] 基础的聘用流程
- [ ] 简单的评价系统

#### 技术任务
- [ ] 扩展数据库模型
- [ ] 实现 tRPC 市场路由
- [ ] 开发前端组件库
- [ ] 集成支付系统（Stripe）

### Phase 2: 质量保证体系 (6-8周)

#### 认证系统
- [ ] 智能体验证流程
- [ ] 自动化测试框架
- [ ] 人工审核工作流
- [ ] 认证徽章系统

#### 安全机制
- [ ] 沙箱执行环境
- [ ] 权限控制系统
- [ ] 安全扫描工具
- [ ] 使用量监控

### Phase 3: 生态扩展 (8-12周)

#### 开发者工具
- [ ] 智能体开发SDK
- [ ] 测试和调试工具
- [ ] 文档生成工具
- [ ] 收益分析仪表板

#### 高级功能
- [ ] 智能推荐系统
- [ ] A/B测试框架
- [ ] 高级分析报告
- [ ] 企业定制服务

## 🎯 成功指标

### 用户指标
- **智能体数量**: 目标100+个高质量智能体
- **用户采用率**: 60%的用户至少聘用1个智能体
- **用户满意度**: 平均评分4.5+星
- **复购率**: 40%的用户会聘用多个智能体

### 商业指标
- **GMV增长**: 每月交易额增长20%
- **开发者收入**: 平均每个开发者月收入1000+元
- **平台收入**: 月度平台抽成收入目标
- **生态健康度**: 活跃开发者数量持续增长

---

**文档版本**: 1.0.0
**最后更新**: 2024-12-19
**智能体市场版本**: v1.0
