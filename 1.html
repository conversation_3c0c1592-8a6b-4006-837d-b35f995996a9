<html><head>
<meta charset="utf-8"/>
<link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
<link as="style" href="https://fonts.googleapis.com/css2?display=swap&amp;family=Manrope%3Awght%40400%3B500%3B700%3B800&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900" onload="this.rel='stylesheet'" rel="stylesheet"/>
<title>Stitch Design</title>
<link href="data:image/x-icon;base64," rel="icon" type="image/x-icon"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<style type="text/tailwindcss">
      :root {
        --primary-color: #ea2a33;
      }
    </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
  </head>
<body class="bg-gray-950">
<div class="relative flex size-full min-h-screen flex-col bg-[#111827] dark justify-between group/design-root overflow-x-hidden" style='font-family: Manrope, "Noto Sans", sans-serif;'>
<div class="flex-grow">
<header class="flex items-center justify-between p-4 pb-2">
<div class="w-12"></div>
<h1 class="text-white text-lg font-bold leading-tight tracking-[-0.015em] flex-1 text-center">Dashboard</h1>
<div class="flex w-12 items-center justify-end">
<button class="flex cursor-pointer items-center justify-center rounded-full h-12 w-12 bg-transparent text-white gap-2 text-base font-bold leading-normal tracking-[0.015em] min-w-0 p-0">
<div class="text-gray-400" data-icon="Gear" data-size="24px" data-weight="regular">
<svg fill="currentColor" height="24px" viewBox="0 0 256 256" width="24px" xmlns="http://www.w3.org/2000/svg">
<path d="M128,80a48,48,0,1,0,48,48A48.05,48.05,0,0,0,128,80Zm0,80a32,32,0,1,1,32-32A32,32,0,0,1,128,160Zm88-29.84q.06-2.16,0-4.32l14.92-18.64a8,8,0,0,0,1.48-7.06,107.21,107.21,0,0,0-10.88-26.25,8,8,0,0,0-6-3.93l-23.72-2.64q-1.48-1.56-3-3L186,40.54a8,8,0,0,0-3.94-6,107.71,107.71,0,0,0-26.25-10.87,8,8,0,0,0-7.06,1.49L130.16,40Q128,40,125.84,40L107.2,25.11a8,8,0,0,0-7.06-1.48A107.6,107.6,0,0,0,73.89,34.51a8,8,0,0,0-3.93,6L67.32,64.27q-1.56,1.49-3,3L40.54,70a8,8,0,0,0-6,3.94,107.71,107.71,0,0,0-10.87,26.25,8,8,0,0,0,1.49,7.06L40,125.84Q40,128,40,130.16L25.11,148.8a8,8,0,0,0-1.48,7.06,107.21,107.21,0,0,0,10.88,26.25,8,8,0,0,0,6,3.93l23.72,2.64q1.49,1.56,3,3L70,215.46a8,8,0,0,0,3.94,6,107.71,107.71,0,0,0,26.25,10.87,8,8,0,0,0,7.06-1.49L125.84,216q2.16.06,4.32,0l18.64,14.92a8,8,0,0,0,7.06,1.48,107.21,107.21,0,0,0,26.25-10.88,8,8,0,0,0,3.93-6l2.64-23.72q1.56-1.48,3-3L215.46,186a8,8,0,0,0,6-3.94,107.71,107.71,0,0,0,10.87-26.25,8,8,0,0,0-1.49-7.06Zm-16.1-6.5a73.93,73.93,0,0,1,0,8.68,8,8,0,0,0,1.74,5.48l14.19,17.73a91.57,91.57,0,0,1-6.23,15L187,173.11a8,8,0,0,0-5.1,2.64,74.11,74.11,0,0,1-6.14,6.14,8,8,0,0,0-2.64,5.1l-2.51,22.58a91.32,91.32,0,0,1-15,6.23l-17.74-14.19a8,8,0,0,0-5-1.75h-.48a73.93,73.93,0,0,1-8.68,0,8,8,0,0,0-5.48,1.74L100.45,215.8a91.57,91.57,0,0,1-15-6.23L82.89,187a8,8,0,0,0-2.64-5.1,74.11,74.11,0,0,1-6.14-6.14,8,8,0,0,0-5.1-2.64L46.43,170.6a91.32,91.32,0,0,1-6.23-15l14.19-17.74a8,8,0,0,0,1.74-5.48,73.93,73.93,0,0,1,0-8.68,8,8,0,0,0-1.74-5.48L40.2,100.45a91.57,91.57,0,0,1,6.23-15L69,82.89a8,8,0,0,0,5.1-2.64,74.11,74.11,0,0,1,6.14-6.14A8,8,0,0,0,82.89,69L85.4,46.43a91.32,91.32,0,0,1,15-6.23l17.74,14.19a8,8,0,0,0,5.48,1.74,73.93,73.93,0,0,1,8.68,0,8,8,0,0,0,5.48-1.74L155.55,40.2a91.57,91.57,0,0,1,15,6.23L173.11,69a8,8,0,0,0,2.64,5.1,74.11,74.11,0,0,1,6.14,6.14,8,8,0,0,0,5.1,2.64l22.58,2.51a91.32,91.32,0,0,1,6.23,15l-14.19,17.74A8,8,0,0,0,199.87,123.66Z"></path>
</svg>
</div>
</button>
</div>
</header>
<main class="px-4">
<section class="mt-5">
<h2 class="text-white text-[22px] font-bold leading-tight tracking-[-0.015em] mb-3">Active Workflows</h2>
<div class="flex overflow-y-auto [-ms-scrollbar-style:none] [scrollbar-width:none] [&amp;::-webkit-scrollbar]:hidden -mx-4 px-4">
<div class="flex items-stretch gap-4">
<div class="flex flex-col gap-3 rounded-lg min-w-40">
<div class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-xl" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuDhgBTL9l3nrfMbbQlaT2sI6rTppS_rZHneu83KVQJxoXpMSOjsO5c1s6q_o7TWAzC6D07G1yILeYv133MlOZI-8lK6nIVD_Ht0Yl6TkMdRdYm4Nbwan4eGJcW0ZGjt-ERVZPtL61GP0SjJhkIWW4EjrSobt4cq6ZouBXzgDTjdlwwDV5HvmbLGsv-og854lT5diEX1s9nR9pp7sznvkfw3z0QOi9I7xx-0MnIWvET5LY9_JnslCKoY7HgFetN5lqjK1n6MerVyx6I");'></div>
<div>
<p class="text-white text-base font-medium leading-normal">Automated Reporting</p>
<p class="text-gray-400 text-sm font-normal leading-normal">Status: <span class="text-green-400">Running</span></p>
</div>
</div>
<div class="flex flex-col gap-3 rounded-lg min-w-40">
<div class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-xl" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuBK4NoS7O6cNpQA0oC2Z7Zmc0jqD3b7BM4faisLtEN6NoY2GhhXhA6d06Bimcff397R3O7jcTm7G6QnIWQQAXJYf6LzeYa1bZZwvV5_KJGFYFBUSqFYiKMNmLNk_2Da9CH0qSX4CynRjxgCa5nrn8XBG9EztsODWxfr57hXRQUa8dTxuIjsl_mHU3EEMbs6ldpnnwYu2aF-ibDill4C_sUYmeS1M_nBW8ivNayf2NiWcF5fq5a9YvXuB2Qdi-t5Djo680GjLH3LJUw");'></div>
<div>
<p class="text-white text-base font-medium leading-normal">Data Analysis</p>
<p class="text-gray-400 text-sm font-normal leading-normal">Status: <span class="text-yellow-400">Paused</span></p>
</div>
</div>
<div class="flex flex-col gap-3 rounded-lg min-w-40">
<div class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-xl" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuDicyRAJys0ypMEmkVOHCwtu7zDaStEZzs3MjcazwDXFQkdTdLDjIBPBWM5Zp7gRkwFrhTIvPn3VvBymxpVQFK_EMjQzdD_1UwBhPYsI59FPzkOyzaF5BBImv6zW5Ck1j8P8wsLsTUvj1REVUVvRuTUNt76tmMd4ylrSyc0iZyQx-ERRUPN8OhWzTOxg6x2QPwrZ5_4g6FN0oJpXQRcCg3avxqB0n_EBJwQrb7HgJFheaCkwt3N-DAjszed0wXKCRA_1djpZR3VIpU");'></div>
<div>
<p class="text-white text-base font-medium leading-normal">Content Creation</p>
<p class="text-gray-400 text-sm font-normal leading-normal">Status: <span class="text-gray-500">Completed</span></p>
</div>
</div>
</div>
</div>
</section>
<section class="mt-8">
<h2 class="text-white text-[22px] font-bold leading-tight tracking-[-0.015em] mb-3">Recent Activity</h2>
<div class="space-y-2">
<div class="flex items-center gap-4 bg-gray-800/50 p-3 rounded-xl min-h-[72px]">
<div class="text-green-400 flex items-center justify-center rounded-lg bg-gray-700 shrink-0 size-12" data-icon="CheckCircle" data-size="24px" data-weight="regular">
<svg fill="currentColor" height="24px" viewBox="0 0 256 256" width="24px" xmlns="http://www.w3.org/2000/svg">
<path d="M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm-16,88a8,8,0,0,1,0-11.31L123.31,89.37a8,8,0,0,1,11.32,0L146,100.69l34.34-34.35a8,8,0,0,1,11.32,11.32l-40,40a8,8,0,0,1-11.32,0Z" fill-rule="evenodd"></path>
</svg>
</div>
<div class="flex flex-col justify-center">
<p class="text-white text-base font-medium leading-normal line-clamp-1">Report generated successfully</p>
<p class="text-gray-400 text-sm font-normal leading-normal line-clamp-2">Workflow: Automated Reporting</p>
</div>
</div>
<div class="flex items-center gap-4 bg-gray-800/50 p-3 rounded-xl min-h-[72px]">
<div class="text-green-400 flex items-center justify-center rounded-lg bg-gray-700 shrink-0 size-12" data-icon="CheckCircle" data-size="24px" data-weight="regular">
<svg fill="currentColor" height="24px" viewBox="0 0 256 256" width="24px" xmlns="http://www.w3.org/2000/svg">
<path d="M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm-16,88a8,8,0,0,1,0-11.31L123.31,89.37a8,8,0,0,1,11.32,0L146,100.69l34.34-34.35a8,8,0,0,1,11.32,11.32l-40,40a8,8,0,0,1-11.32,0Z" fill-rule="evenodd"></path>
</svg>
</div>
<div class="flex flex-col justify-center">
<p class="text-white text-base font-medium leading-normal line-clamp-1">Data analysis completed</p>
<p class="text-gray-400 text-sm font-normal leading-normal line-clamp-2">Agent: Data Analyst</p>
</div>
</div>
<div class="flex items-center gap-4 bg-gray-800/50 p-3 rounded-xl min-h-[72px]">
<div class="text-green-400 flex items-center justify-center rounded-lg bg-gray-700 shrink-0 size-12" data-icon="CheckCircle" data-size="24px" data-weight="regular">
<svg fill="currentColor" height="24px" viewBox="0 0 256 256" width="24px" xmlns="http://www.w3.org/2000/svg">
<path d="M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm-16,88a8,8,0,0,1,0-11.31L123.31,89.37a8,8,0,0,1,11.32,0L146,100.69l34.34-34.35a8,8,0,0,1,11.32,11.32l-40,40a8,8,0,0,1-11.32,0Z" fill-rule="evenodd"></path>
</svg>
</div>
<div class="flex flex-col justify-center">
<p class="text-white text-base font-medium leading-normal line-clamp-1">Content draft ready for review</p>
<p class="text-gray-400 text-sm font-normal leading-normal line-clamp-2">Workflow: Content Creation Engine</p>
</div>
</div>
</div>
</section>
<section class="mt-8 mb-4">
<h2 class="text-white text-[22px] font-bold leading-tight tracking-[-0.015em] mb-3">Key Metrics</h2>
<div class="grid grid-cols-2 gap-4">
<div class="flex flex-col gap-2 rounded-xl p-4 bg-gray-800/50">
<p class="text-gray-300 text-base font-medium leading-normal">Total Workflows</p>
<p class="text-white tracking-light text-3xl font-bold leading-tight">25</p>
</div>
<div class="flex flex-col gap-2 rounded-xl p-4 bg-gray-800/50">
<p class="text-gray-300 text-base font-medium leading-normal">Active Agents</p>
<p class="text-white tracking-light text-3xl font-bold leading-tight">5</p>
</div>
</div>
</section>
</main>
</div>
<footer class="sticky bottom-0">
<div class="flex border-t border-gray-700/50 bg-gray-900/80 backdrop-blur-lg px-4 pb-3 pt-2">
<a class="flex flex-1 flex-col items-center justify-end gap-1 rounded-full text-[var(--primary-color)]" href="#">
<div class="flex h-8 items-center justify-center" data-icon="House" data-size="24px" data-weight="fill">
<svg fill="currentColor" height="24px" viewBox="0 0 256 256" width="24px" xmlns="http://www.w3.org/2000/svg">
<path d="M224,115.55V208a16,16,0,0,1-16,16H168a16,16,0,0,1-16-16V168a8,8,0,0,0-8-8H112a8,8,0,0,0-8,8v40a16,16,0,0,1-16,16H48a16,16,0,0,1-16-16V115.55a16,16,0,0,1,5.17-11.78l80-75.48.11-.11a16,16,0,0,1,21.53,0,1.14,1.14,0,0,0,.11.11l80,75.48A16,16,0,0,1,224,115.55Z"></path>
</svg>
</div>
<p class="text-xs font-medium leading-normal tracking-[0.015em]">Dashboard</p>
</a>
<a class="flex flex-1 flex-col items-center justify-end gap-1 text-gray-400" href="#">
<div class="flex h-8 items-center justify-center" data-icon="ListBullets" data-size="24px" data-weight="regular">
<svg fill="currentColor" height="24px" viewBox="0 0 256 256" width="24px" xmlns="http://www.w3.org/2000/svg">
<path d="M80,64a8,8,0,0,1,8-8H216a8,8,0,0,1,0,16H88A8,8,0,0,1,80,64Zm136,56H88a8,8,0,0,0,0,16H216a8,8,0,0,0,0-16Zm0,64H88a8,8,0,0,0,0,16H216a8,8,0,0,0,0-16ZM44,52A12,12,0,1,0,56,64,12,12,0,0,0,44,52Zm0,64a12,12,0,1,0,12,12A12,12,0,0,0,44,116Zm0,64a12,12,0,1,0,12,12A12,12,0,0,0,44,180Z"></path>
</svg>
</div>
<p class="text-xs font-medium leading-normal tracking-[0.015em]">Workflows</p>
</a>
<a class="flex flex-1 flex-col items-center justify-end gap-1 text-gray-400" href="#">
<div class="flex h-8 items-center justify-center" data-icon="Users" data-size="24px" data-weight="regular">
<svg fill="currentColor" height="24px" viewBox="0 0 256 256" width="24px" xmlns="http://www.w3.org/2000/svg">
<path d="M117.25,157.92a60,60,0,1,0-66.5,0A95.83,95.83,0,0,0,3.53,195.63a8,8,0,1,0,13.4,8.74,80,80,0,0,1,134.14,0,8,8,0,0,0,13.4-8.74A95.83,95.83,0,0,0,117.25,157.92ZM40,108a44,44,0,1,1,44,44A44.05,44.05,0,0,1,40,108Zm210.14,98.7a8,8,0,0,1-11.07-2.33A79.83,79.83,0,0,0,172,168a8,8,0,0,1,0-16,44,44,0,1,0-16.34-84.87,8,8,0,1,1-5.94-14.85,60,60,0,0,1,55.53,105.64,95.83,95.83,0,0,1,47.22,37.71A8,8,0,0,1,250.14,206.7Z"></path>
</svg>
</div>
<p class="text-xs font-medium leading-normal tracking-[0.015em]">Agents</p>
</a>
<a class="flex flex-1 flex-col items-center justify-end gap-1 text-gray-400" href="#">
<div class="flex h-8 items-center justify-center" data-icon="BookOpen" data-size="24px" data-weight="regular">
<svg fill="currentColor" height="24px" viewBox="0 0 256 256" width="24px" xmlns="http://www.w3.org/2000/svg">
<path d="M224,48H160a40,40,0,0,0-32,16A40,40,0,0,0,96,48H32A16,16,0,0,0,16,64V192a16,16,0,0,0,16,16H96a24,24,0,0,1,24,24,8,8,0,0,0,16,0,24,24,0,0,1,24-24h64a16,16,0,0,0,16-16V64A16,16,0,0,0,224,48ZM96,192H32V64H96a24,24,0,0,1,24,24V200A39.81,39.81,0,0,0,96,192Zm128,0H160a39.81,39.81,0,0,0-24,8V88a24,24,0,0,1,24-24h64Z"></path>
</svg>
</div>
<p class="text-xs font-medium leading-normal tracking-[0.015em]">Knowledge</p>
</a>
</div>
<div class="bg-gray-900/80 backdrop-blur-lg h-safe-area-bottom"></div>
</footer>
</div>

</body></html>